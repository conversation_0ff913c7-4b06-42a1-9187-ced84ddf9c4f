"""
Field Representation Module

This module provides classes for representing the parking field as a 2D grid
with entrances, roads, and parking spaces.
"""

import numpy as np
from typing import List, Tuple, Dict, Optional, Set
from enum import Enum
import yaml


class CellType(Enum):
    """Enumeration for different types of grid cells."""
    EMPTY = 0
    PARKING = 1
    ROAD = 2
    ENTRANCE = 3
    BLOCKED = 4


class ParkingField:
    """
    Represents the parking field as a 2D grid with various cell types.
    """
    
    def __init__(self, width: float, height: float, resolution: float):
        """
        Initialize the parking field.
        
        Args:
            width: Field width in meters
            height: Field height in meters
            resolution: Grid resolution in meters per cell
        """
        self.width = width
        self.height = height
        self.resolution = resolution
        
        # Grid dimensions
        self.grid_width = int(np.ceil(width / resolution))
        self.grid_height = int(np.ceil(height / resolution))
        
        # Initialize grid with empty cells
        self.grid = np.full((self.grid_width, self.grid_height), 
                           CellType.EMPTY, dtype=object)
        
        # Store entrance and road information
        self.entrances: List[Tuple[int, int]] = []
        self.roads: Set[Tuple[int, int]] = set()
        self.parking_spaces: Set[Tuple[int, int]] = set()
        
    def world_to_grid(self, x: float, y: float) -> Tuple[int, int]:
        """Convert world coordinates to grid coordinates."""
        grid_x = int(x / self.resolution)
        grid_y = int(y / self.resolution)
        return grid_x, grid_y
    
    def grid_to_world(self, grid_x: int, grid_y: int) -> Tuple[float, float]:
        """Convert grid coordinates to world coordinates (center of cell)."""
        x = (grid_x + 0.5) * self.resolution
        y = (grid_y + 0.5) * self.resolution
        return x, y
    
    def is_valid_position(self, grid_x: int, grid_y: int) -> bool:
        """Check if grid position is within field boundaries."""
        return (0 <= grid_x < self.grid_width and 
                0 <= grid_y < self.grid_height)
    
    def add_entrance(self, x: float, y: float, width: float = 6.0):
        """
        Add an entrance to the field.
        
        Args:
            x, y: World coordinates of entrance center
            width: Width of entrance in meters
        """
        grid_x, grid_y = self.world_to_grid(x, y)
        width_cells = int(np.ceil(width / self.resolution))
        
        # Mark entrance cells
        for dx in range(-width_cells//2, width_cells//2 + 1):
            for dy in range(-width_cells//2, width_cells//2 + 1):
                gx, gy = grid_x + dx, grid_y + dy
                if self.is_valid_position(gx, gy):
                    self.grid[gx, gy] = CellType.ENTRANCE
                    self.entrances.append((gx, gy))
    
    def add_road_segment(self, start_x: float, start_y: float, 
                        end_x: float, end_y: float, width: float = 6.0):
        """
        Add a road segment between two points.
        
        Args:
            start_x, start_y: Starting point in world coordinates
            end_x, end_y: Ending point in world coordinates
            width: Road width in meters
        """
        start_grid = self.world_to_grid(start_x, start_y)
        end_grid = self.world_to_grid(end_x, end_y)
        
        # Get all grid cells along the road path
        road_cells = self._get_line_cells(start_grid, end_grid, width)
        
        for gx, gy in road_cells:
            if self.is_valid_position(gx, gy):
                self.grid[gx, gy] = CellType.ROAD
                self.roads.add((gx, gy))
    
    def _get_line_cells(self, start: Tuple[int, int], end: Tuple[int, int], 
                       width: float) -> List[Tuple[int, int]]:
        """Get all grid cells that form a line with given width."""
        x1, y1 = start
        x2, y2 = end
        
        cells = []
        width_cells = int(np.ceil(width / self.resolution))
        
        # Bresenham's line algorithm with width
        dx = abs(x2 - x1)
        dy = abs(y2 - y1)
        sx = 1 if x1 < x2 else -1
        sy = 1 if y1 < y2 else -1
        err = dx - dy
        
        x, y = x1, y1
        
        while True:
            # Add cells around current point for road width
            for dx_offset in range(-width_cells//2, width_cells//2 + 1):
                for dy_offset in range(-width_cells//2, width_cells//2 + 1):
                    cells.append((x + dx_offset, y + dy_offset))
            
            if x == x2 and y == y2:
                break
                
            e2 = 2 * err
            if e2 > -dy:
                err -= dy
                x += sx
            if e2 < dx:
                err += dx
                y += sy
        
        return cells
    
    def place_parking_space(self, x: float, y: float, 
                           length: float = 4.5, width: float = 2.0) -> bool:
        """
        Place a parking space at the given location.
        
        Args:
            x, y: World coordinates of parking space center
            length, width: Dimensions of parking space
            
        Returns:
            True if placement successful, False otherwise
        """
        grid_x, grid_y = self.world_to_grid(x, y)
        length_cells = int(np.ceil(length / self.resolution))
        width_cells = int(np.ceil(width / self.resolution))
        
        # Check if space is available
        for dx in range(length_cells):
            for dy in range(width_cells):
                gx = grid_x - length_cells//2 + dx
                gy = grid_y - width_cells//2 + dy
                
                if not self.is_valid_position(gx, gy):
                    return False
                    
                if self.grid[gx, gy] != CellType.EMPTY:
                    return False
        
        # Place parking space
        for dx in range(length_cells):
            for dy in range(width_cells):
                gx = grid_x - length_cells//2 + dx
                gy = grid_y - width_cells//2 + dy
                self.grid[gx, gy] = CellType.PARKING
                self.parking_spaces.add((gx, gy))
        
        return True
    
    def remove_parking_space(self, x: float, y: float):
        """Remove parking space at given location."""
        grid_x, grid_y = self.world_to_grid(x, y)
        
        # Find and remove all cells of this parking space
        to_remove = []
        for gx, gy in self.parking_spaces:
            if abs(gx - grid_x) <= 2 and abs(gy - grid_y) <= 2:
                self.grid[gx, gy] = CellType.EMPTY
                to_remove.append((gx, gy))
        
        for cell in to_remove:
            self.parking_spaces.discard(cell)
    
    def is_accessible(self, parking_x: int, parking_y: int, 
                     max_distance: float = 10.0) -> bool:
        """
        Check if a parking space is accessible from roads.
        
        Args:
            parking_x, parking_y: Grid coordinates of parking space
            max_distance: Maximum acceptable distance to road
            
        Returns:
            True if accessible, False otherwise
        """
        max_dist_cells = int(max_distance / self.resolution)
        
        # Check if there's a road within max_distance
        for road_x, road_y in self.roads:
            dist = np.sqrt((parking_x - road_x)**2 + (parking_y - road_y)**2)
            if dist <= max_dist_cells:
                return True
        
        return False
    
    def get_walking_distance(self, parking_x: int, parking_y: int) -> float:
        """
        Calculate minimum walking distance from parking space to any entrance.
        
        Args:
            parking_x, parking_y: Grid coordinates of parking space
            
        Returns:
            Minimum walking distance in meters
        """
        if not self.entrances:
            return float('inf')
        
        min_distance = float('inf')
        
        for entrance_x, entrance_y in self.entrances:
            # Simple Euclidean distance (could be improved with pathfinding)
            grid_dist = np.sqrt((parking_x - entrance_x)**2 + 
                              (parking_y - entrance_y)**2)
            world_dist = grid_dist * self.resolution
            min_distance = min(min_distance, world_dist)
        
        return min_distance
    
    def get_field_statistics(self) -> Dict[str, int]:
        """Get statistics about the current field configuration."""
        stats = {
            'total_cells': self.grid_width * self.grid_height,
            'empty_cells': 0,
            'parking_cells': 0,
            'road_cells': 0,
            'entrance_cells': 0,
            'blocked_cells': 0
        }
        
        for i in range(self.grid_width):
            for j in range(self.grid_height):
                cell_type = self.grid[i, j]
                if cell_type == CellType.EMPTY:
                    stats['empty_cells'] += 1
                elif cell_type == CellType.PARKING:
                    stats['parking_cells'] += 1
                elif cell_type == CellType.ROAD:
                    stats['road_cells'] += 1
                elif cell_type == CellType.ENTRANCE:
                    stats['entrance_cells'] += 1
                elif cell_type == CellType.BLOCKED:
                    stats['blocked_cells'] += 1
        
        return stats
    
    def save_configuration(self, filename: str):
        """Save field configuration to file."""
        config = {
            'field': {
                'width': self.width,
                'height': self.height,
                'resolution': self.resolution
            },
            'entrances': [self.grid_to_world(x, y) for x, y in self.entrances],
            'parking_spaces': [self.grid_to_world(x, y) for x, y in self.parking_spaces],
            'roads': [self.grid_to_world(x, y) for x, y in self.roads]
        }
        
        with open(filename, 'w') as f:
            yaml.dump(config, f, default_flow_style=False)
    
    def load_configuration(self, filename: str):
        """Load field configuration from file."""
        with open(filename, 'r') as f:
            config = yaml.safe_load(f)
        
        # Reset field
        self.__init__(config['field']['width'], 
                     config['field']['height'], 
                     config['field']['resolution'])
        
        # Add entrances
        for x, y in config.get('entrances', []):
            self.add_entrance(x, y)
        
        # Add parking spaces
        for x, y in config.get('parking_spaces', []):
            self.place_parking_space(x, y)
        
        # Add roads (simplified - would need start/end points in real implementation)
        for x, y in config.get('roads', []):
            grid_x, grid_y = self.world_to_grid(x, y)
            if self.is_valid_position(grid_x, grid_y):
                self.grid[grid_x, grid_y] = CellType.ROAD
                self.roads.add((grid_x, grid_y))
