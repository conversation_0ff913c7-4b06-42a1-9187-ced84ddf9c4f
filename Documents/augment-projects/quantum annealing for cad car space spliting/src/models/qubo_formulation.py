"""
QUBO Formulation for Parking Space Optimization

This module implements the mathematical formulation of the parking optimization
problem as a Quadratic Unconstrained Binary Optimization (QUBO) problem.
"""

import numpy as np
from typing import Dict, List, Tuple, Optional
import networkx as nx


class ParkingQUBO:
    """
    Formulates the parking optimization problem as a QUBO.
    
    Decision Variables:
    - x_ij: Binary variable indicating if a parking space is placed at grid position (i,j)
    - r_ij: Binary variable indicating if a road segment exists at position (i,j)
    
    Objective Function:
    minimize: -w1 * (parking_capacity) + w2 * (walking_distance) + w3 * (traffic_penalty)
    
    Subject to constraints:
    - Physical spacing constraints
    - Road accessibility constraints
    - Traffic flow constraints
    - Field boundary constraints
    """
    
    def __init__(self, field_width: float, field_height: float, resolution: float,
                 car_length: float, car_width: float, road_width: float,
                 entrances: List[Tuple[float, float]], weights: Dict[str, float]):
        """
        Initialize the QUBO formulation.
        
        Args:
            field_width: Width of the field in meters
            field_height: Height of the field in meters
            resolution: Grid resolution in meters
            car_length: Length of a car in meters
            car_width: Width of a car in meters
            road_width: Width of roads in meters
            entrances: List of entrance coordinates
            weights: Objective function weights
        """
        self.field_width = field_width
        self.field_height = field_height
        self.resolution = resolution
        self.car_length = car_length
        self.car_width = car_width
        self.road_width = road_width
        self.entrances = entrances
        self.weights = weights
        
        # Grid dimensions
        self.grid_width = int(field_width / resolution)
        self.grid_height = int(field_height / resolution)
        
        # Variable indices
        self.parking_vars = {}  # (i,j) -> variable index for parking spaces
        self.road_vars = {}     # (i,j) -> variable index for road segments
        self.var_count = 0
        
        self._initialize_variables()
        
    def _initialize_variables(self):
        """Initialize decision variables for parking spaces and roads."""
        # Parking space variables
        for i in range(self.grid_width):
            for j in range(self.grid_height):
                if self._is_valid_parking_position(i, j):
                    self.parking_vars[(i, j)] = self.var_count
                    self.var_count += 1
        
        # Road variables
        for i in range(self.grid_width):
            for j in range(self.grid_height):
                if self._is_valid_road_position(i, j):
                    self.road_vars[(i, j)] = self.var_count
                    self.var_count += 1
    
    def _is_valid_parking_position(self, i: int, j: int) -> bool:
        """Check if position (i,j) can accommodate a parking space."""
        # Convert grid coordinates to real coordinates
        x = i * self.resolution
        y = j * self.resolution
        
        # Check if car fits within field boundaries
        if (x + self.car_length > self.field_width or 
            y + self.car_width > self.field_height):
            return False
            
        # Check if too close to entrances
        for entrance_x, entrance_y in self.entrances:
            dist = np.sqrt((x - entrance_x)**2 + (y - entrance_y)**2)
            if dist < self.road_width:
                return False
                
        return True
    
    def _is_valid_road_position(self, i: int, j: int) -> bool:
        """Check if position (i,j) can be part of a road."""
        x = i * self.resolution
        y = j * self.resolution
        return (0 <= x < self.field_width and 0 <= y < self.field_height)
    
    def build_qubo_matrix(self) -> np.ndarray:
        """
        Build the QUBO matrix Q where the objective is x^T Q x.
        
        Returns:
            QUBO matrix of size (var_count, var_count)
        """
        Q = np.zeros((self.var_count, self.var_count))
        
        # Objective terms
        self._add_parking_capacity_terms(Q)
        self._add_walking_distance_terms(Q)
        self._add_traffic_flow_terms(Q)
        
        # Constraint terms (as penalties)
        self._add_spacing_constraints(Q)
        self._add_accessibility_constraints(Q)
        self._add_road_connectivity_constraints(Q)

        # Ensure matrix is symmetric
        Q = (Q + Q.T) / 2

        return Q
    
    def _add_parking_capacity_terms(self, Q: np.ndarray):
        """Add terms to maximize parking capacity."""
        weight = -self.weights['parking_capacity']  # Negative for maximization
        
        for (i, j), var_idx in self.parking_vars.items():
            Q[var_idx, var_idx] += weight
    
    def _add_walking_distance_terms(self, Q: np.ndarray):
        """Add terms to minimize walking distance from parking to entrances."""
        weight = self.weights['walking_distance']
        
        for (i, j), var_idx in self.parking_vars.items():
            # Calculate minimum distance to any entrance
            x, y = i * self.resolution, j * self.resolution
            min_dist = min(
                np.sqrt((x - ex)**2 + (y - ey)**2) 
                for ex, ey in self.entrances
            )
            
            # Add quadratic penalty proportional to distance
            Q[var_idx, var_idx] += weight * min_dist
    
    def _add_traffic_flow_terms(self, Q: np.ndarray):
        """Add terms to optimize traffic flow efficiency."""
        weight = self.weights['traffic_flow']
        
        # Penalty for parking spaces that block optimal traffic paths
        for (i, j), park_var in self.parking_vars.items():
            for (ri, rj), road_var in self.road_vars.items():
                if self._blocks_traffic_flow(i, j, ri, rj):
                    Q[park_var, road_var] += weight
    
    def _add_spacing_constraints(self, Q: np.ndarray):
        """Add constraints for minimum spacing between parking spaces."""
        penalty = 1000.0  # Large penalty for constraint violation
        
        min_spacing_grid = int(0.5 / self.resolution)  # 0.5m minimum spacing
        
        for (i1, j1), var1 in self.parking_vars.items():
            for (i2, j2), var2 in self.parking_vars.items():
                if var1 < var2:  # Avoid double counting
                    grid_dist = max(abs(i1 - i2), abs(j1 - j2))
                    if grid_dist <= min_spacing_grid:
                        # Penalty for both spaces being occupied
                        Q[var1, var2] += penalty
    
    def _add_accessibility_constraints(self, Q: np.ndarray):
        """Add constraints ensuring parking spaces are accessible from roads."""
        penalty = 500.0
        
        for (i, j), park_var in self.parking_vars.items():
            # Check if there's a road within accessible distance
            accessible = False
            for (ri, rj), road_var in self.road_vars.items():
                if self._is_accessible_from_road(i, j, ri, rj):
                    # Encourage parking near roads
                    Q[park_var, road_var] -= penalty * 0.1
                    accessible = True
            
            if not accessible:
                # Heavy penalty for inaccessible parking
                Q[park_var, park_var] += penalty
    
    def _add_road_connectivity_constraints(self, Q: np.ndarray):
        """Add constraints ensuring roads form connected paths to entrances."""
        penalty = 300.0
        
        # Build road network graph
        road_graph = nx.Graph()
        
        # Add road segments as nodes
        for (i, j) in self.road_vars.keys():
            road_graph.add_node((i, j))
        
        # Add edges between adjacent road segments
        for (i1, j1) in self.road_vars.keys():
            for (i2, j2) in self.road_vars.keys():
                if abs(i1 - i2) + abs(j1 - j2) == 1:  # Adjacent cells
                    road_graph.add_edge((i1, j1), (i2, j2))
        
        # Penalty for disconnected road segments
        for (i, j), road_var in self.road_vars.items():
            connectivity_score = len(list(road_graph.neighbors((i, j))))
            if connectivity_score == 0:
                Q[road_var, road_var] += penalty
    
    def _blocks_traffic_flow(self, park_i: int, park_j: int, 
                           road_i: int, road_j: int) -> bool:
        """Check if parking space blocks traffic flow on road segment."""
        # Simple heuristic: parking space blocks road if too close
        dist = abs(park_i - road_i) + abs(park_j - road_j)
        return dist <= 2  # Within 2 grid cells
    
    def _is_accessible_from_road(self, park_i: int, park_j: int,
                               road_i: int, road_j: int) -> bool:
        """Check if parking space is accessible from road segment."""
        # Parking space is accessible if within reasonable distance of road
        dist = np.sqrt((park_i - road_i)**2 + (park_j - road_j)**2)
        max_access_dist = int(3.0 / self.resolution)  # 3 meters max
        return dist <= max_access_dist
    
    def get_variable_mapping(self) -> Dict[int, Tuple[str, int, int]]:
        """
        Get mapping from variable index to (type, i, j).
        
        Returns:
            Dictionary mapping variable index to (variable_type, grid_i, grid_j)
        """
        mapping = {}
        
        for (i, j), var_idx in self.parking_vars.items():
            mapping[var_idx] = ('parking', i, j)
            
        for (i, j), var_idx in self.road_vars.items():
            mapping[var_idx] = ('road', i, j)
            
        return mapping
