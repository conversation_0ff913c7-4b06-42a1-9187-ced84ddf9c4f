"""
Visualization Module for Parking Layout

This module provides visualization tools for displaying optimized parking layouts,
including roads, entrances, parking spaces, and optimization metrics.
"""

import matplotlib.pyplot as plt
import matplotlib.patches as patches
import numpy as np
from typing import Dict, List, Tuple, Optional, Any
import seaborn as sns
from models.field_representation import Parking<PERSON>ield, CellType


class ParkingLayoutVisualizer:
    """
    Visualizes parking field layouts and optimization results.
    """
    
    def __init__(self, field: ParkingField, figsize: Tuple[int, int] = (12, 8)):
        """
        Initialize visualizer.
        
        Args:
            field: ParkingField instance to visualize
            figsize: Figure size for plots
        """
        self.field = field
        self.figsize = figsize
        
        # Color scheme
        self.colors = {
            'empty': '#f0f0f0',
            'parking': '#4CAF50',
            'road': '#757575',
            'entrance': '#FF5722',
            'blocked': '#F44336',
            'grid': '#e0e0e0'
        }
    
    def plot_field_layout(self, title: str = "Parking Field Layout", 
                         show_grid: bool = True, save_path: Optional[str] = None) -> plt.Figure:
        """
        Plot the current field layout.
        
        Args:
            title: Plot title
            show_grid: Whether to show grid lines
            save_path: Path to save the plot (optional)
            
        Returns:
            Matplotlib figure object
        """
        fig, ax = plt.subplots(figsize=self.figsize)
        
        # Create color map for the grid
        color_map = np.zeros((self.field.grid_width, self.field.grid_height, 3))
        
        for i in range(self.field.grid_width):
            for j in range(self.field.grid_height):
                cell_type = self.field.grid[i, j]
                
                if cell_type == CellType.EMPTY:
                    color = self._hex_to_rgb(self.colors['empty'])
                elif cell_type == CellType.PARKING:
                    color = self._hex_to_rgb(self.colors['parking'])
                elif cell_type == CellType.ROAD:
                    color = self._hex_to_rgb(self.colors['road'])
                elif cell_type == CellType.ENTRANCE:
                    color = self._hex_to_rgb(self.colors['entrance'])
                else:  # BLOCKED
                    color = self._hex_to_rgb(self.colors['blocked'])
                
                color_map[i, j] = color
        
        # Display the grid
        ax.imshow(color_map.transpose(1, 0, 2), origin='lower', 
                 extent=[0, self.field.width, 0, self.field.height])
        
        # Add grid lines if requested
        if show_grid:
            self._add_grid_lines(ax)
        
        # Add labels and annotations
        self._add_parking_space_labels(ax)
        self._add_entrance_labels(ax)
        
        # Customize plot
        ax.set_xlabel('X (meters)')
        ax.set_ylabel('Y (meters)')
        ax.set_title(title)
        ax.set_aspect('equal')
        
        # Add legend
        self._add_legend(ax)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        
        return fig
    
    def plot_optimization_progress(self, optimization_result: Dict[str, Any],
                                 save_path: Optional[str] = None) -> plt.Figure:
        """
        Plot optimization progress and convergence.
        
        Args:
            optimization_result: Result from quantum annealing optimization
            save_path: Path to save the plot (optional)
            
        Returns:
            Matplotlib figure object
        """
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        
        # Energy convergence plot
        if 'energy_history' in optimization_result:
            axes[0, 0].plot(optimization_result['energy_history'])
            axes[0, 0].set_title('Energy Convergence')
            axes[0, 0].set_xlabel('Iteration (x100)')
            axes[0, 0].set_ylabel('Energy')
            axes[0, 0].grid(True)
        
        # Temperature schedule (for simulated annealing)
        if 'temperature_history' in optimization_result:
            axes[0, 1].plot(optimization_result['temperature_history'])
            axes[0, 1].set_title('Temperature Schedule')
            axes[0, 1].set_xlabel('Iteration (x100)')
            axes[0, 1].set_ylabel('Temperature')
            axes[0, 1].set_yscale('log')
            axes[0, 1].grid(True)
        
        # Solution quality metrics
        metrics = self._calculate_solution_metrics(optimization_result['solution'])
        metric_names = list(metrics.keys())
        metric_values = list(metrics.values())
        
        axes[1, 0].bar(metric_names, metric_values)
        axes[1, 0].set_title('Solution Quality Metrics')
        axes[1, 0].set_ylabel('Value')
        axes[1, 0].tick_params(axis='x', rotation=45)
        
        # Algorithm comparison (if multiple results available)
        if 'sa_result' in optimization_result and 'dwave_result' in optimization_result:
            algorithms = ['Simulated Annealing', 'D-Wave Quantum']
            energies = [optimization_result['sa_result']['energy'], 
                       optimization_result['dwave_result']['energy']]
            
            axes[1, 1].bar(algorithms, energies)
            axes[1, 1].set_title('Algorithm Comparison')
            axes[1, 1].set_ylabel('Final Energy')
        else:
            axes[1, 1].text(0.5, 0.5, 'Single Algorithm Used', 
                           ha='center', va='center', transform=axes[1, 1].transAxes)
            axes[1, 1].set_title('Algorithm Information')
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        
        return fig
    
    def plot_solution_analysis(self, solution: np.ndarray, variable_mapping: Dict[int, Tuple[str, int, int]],
                             save_path: Optional[str] = None) -> plt.Figure:
        """
        Plot detailed analysis of the optimization solution.
        
        Args:
            solution: Binary solution vector
            variable_mapping: Mapping from variable index to (type, i, j)
            save_path: Path to save the plot (optional)
            
        Returns:
            Matplotlib figure object
        """
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        
        # Extract parking and road placements from solution
        parking_positions = []
        road_positions = []
        
        for var_idx, (var_type, i, j) in variable_mapping.items():
            if solution[var_idx] == 1:
                if var_type == 'parking':
                    parking_positions.append((i, j))
                elif var_type == 'road':
                    road_positions.append((i, j))
        
        # Parking space distribution
        if parking_positions:
            parking_x = [pos[0] * self.field.resolution for pos in parking_positions]
            parking_y = [pos[1] * self.field.resolution for pos in parking_positions]
            
            axes[0, 0].scatter(parking_x, parking_y, c='green', alpha=0.6, s=20)
            axes[0, 0].set_title(f'Parking Spaces Distribution ({len(parking_positions)} spaces)')
            axes[0, 0].set_xlabel('X (meters)')
            axes[0, 0].set_ylabel('Y (meters)')
            axes[0, 0].set_xlim(0, self.field.width)
            axes[0, 0].set_ylim(0, self.field.height)
        
        # Walking distance heatmap
        walking_distances = self._calculate_walking_distance_heatmap()
        im = axes[0, 1].imshow(walking_distances.T, origin='lower', cmap='viridis',
                              extent=[0, self.field.width, 0, self.field.height])
        axes[0, 1].set_title('Walking Distance Heatmap')
        axes[0, 1].set_xlabel('X (meters)')
        axes[0, 1].set_ylabel('Y (meters)')
        plt.colorbar(im, ax=axes[0, 1], label='Distance (meters)')
        
        # Parking density analysis
        density_map = self._calculate_parking_density(parking_positions)
        axes[1, 0].imshow(density_map.T, origin='lower', cmap='Reds',
                         extent=[0, self.field.width, 0, self.field.height])
        axes[1, 0].set_title('Parking Density Map')
        axes[1, 0].set_xlabel('X (meters)')
        axes[1, 0].set_ylabel('Y (meters)')
        
        # Accessibility analysis
        accessibility_scores = self._calculate_accessibility_scores(parking_positions)
        if accessibility_scores:
            axes[1, 1].hist(accessibility_scores, bins=20, alpha=0.7, color='blue')
            axes[1, 1].set_title('Accessibility Score Distribution')
            axes[1, 1].set_xlabel('Accessibility Score')
            axes[1, 1].set_ylabel('Number of Parking Spaces')
            axes[1, 1].axvline(np.mean(accessibility_scores), color='red', 
                              linestyle='--', label=f'Mean: {np.mean(accessibility_scores):.2f}')
            axes[1, 1].legend()
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        
        return fig
    
    def _hex_to_rgb(self, hex_color: str) -> Tuple[float, float, float]:
        """Convert hex color to RGB tuple."""
        hex_color = hex_color.lstrip('#')
        return tuple(int(hex_color[i:i+2], 16) / 255.0 for i in (0, 2, 4))
    
    def _add_grid_lines(self, ax):
        """Add grid lines to the plot."""
        # Vertical lines
        for i in range(0, int(self.field.width) + 1, 5):
            ax.axvline(i, color=self.colors['grid'], linewidth=0.5, alpha=0.5)
        
        # Horizontal lines
        for j in range(0, int(self.field.height) + 1, 5):
            ax.axhline(j, color=self.colors['grid'], linewidth=0.5, alpha=0.5)
    
    def _add_parking_space_labels(self, ax):
        """Add labels for parking spaces."""
        parking_count = 0
        for i in range(self.field.grid_width):
            for j in range(self.field.grid_height):
                if self.field.grid[i, j] == CellType.PARKING:
                    parking_count += 1
        
        ax.text(0.02, 0.98, f'Parking Spaces: {parking_count}', 
               transform=ax.transAxes, verticalalignment='top',
               bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))
    
    def _add_entrance_labels(self, ax):
        """Add labels for entrances."""
        for idx, (entrance_x, entrance_y) in enumerate(self.field.entrances):
            world_x, world_y = self.field.grid_to_world(entrance_x, entrance_y)
            ax.annotate(f'Entrance {idx+1}', (world_x, world_y),
                       xytext=(10, 10), textcoords='offset points',
                       bbox=dict(boxstyle='round,pad=0.3', facecolor='orange', alpha=0.7),
                       arrowprops=dict(arrowstyle='->', connectionstyle='arc3,rad=0'))
    
    def _add_legend(self, ax):
        """Add legend to the plot."""
        legend_elements = [
            patches.Patch(color=self.colors['empty'], label='Empty'),
            patches.Patch(color=self.colors['parking'], label='Parking'),
            patches.Patch(color=self.colors['road'], label='Road'),
            patches.Patch(color=self.colors['entrance'], label='Entrance'),
            patches.Patch(color=self.colors['blocked'], label='Blocked')
        ]
        ax.legend(handles=legend_elements, loc='upper right')
    
    def _calculate_solution_metrics(self, solution: np.ndarray) -> Dict[str, float]:
        """Calculate various metrics for the solution."""
        parking_spaces = np.sum(solution)  # Simplified - assumes all 1s are parking
        
        return {
            'Parking Spaces': parking_spaces,
            'Space Utilization': parking_spaces / len(solution) * 100,
            'Solution Density': np.mean(solution)
        }
    
    def _calculate_walking_distance_heatmap(self) -> np.ndarray:
        """Calculate walking distance heatmap."""
        heatmap = np.zeros((self.field.grid_width, self.field.grid_height))
        
        for i in range(self.field.grid_width):
            for j in range(self.field.grid_height):
                heatmap[i, j] = self.field.get_walking_distance(i, j)
        
        return heatmap
    
    def _calculate_parking_density(self, parking_positions: List[Tuple[int, int]]) -> np.ndarray:
        """Calculate parking density map."""
        density_map = np.zeros((self.field.grid_width, self.field.grid_height))
        
        for park_i, park_j in parking_positions:
            # Add density around each parking space
            for i in range(max(0, park_i - 5), min(self.field.grid_width, park_i + 6)):
                for j in range(max(0, park_j - 5), min(self.field.grid_height, park_j + 6)):
                    distance = np.sqrt((i - park_i)**2 + (j - park_j)**2)
                    if distance <= 5:
                        density_map[i, j] += 1 / (distance + 1)
        
        return density_map
    
    def _calculate_accessibility_scores(self, parking_positions: List[Tuple[int, int]]) -> List[float]:
        """Calculate accessibility scores for parking positions."""
        scores = []
        
        for park_i, park_j in parking_positions:
            if self.field.is_accessible(park_i, park_j):
                walking_dist = self.field.get_walking_distance(park_i, park_j)
                # Higher score = better accessibility (inverse of walking distance)
                score = 100.0 / (walking_dist + 1)
                scores.append(score)
        
        return scores
