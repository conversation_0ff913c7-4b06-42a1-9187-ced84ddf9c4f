"""
Main Application for Quantum Annealing Parking Optimization

This is the main entry point for the parking space optimization system.
It coordinates all components to solve the parking layout problem using quantum annealing.
"""

import argparse
import yaml
import numpy as np
import os
import json
from typing import Dict, Any, Optional
import matplotlib.pyplot as plt

from models.field_representation import Parking<PERSON>ield
from models.qubo_formulation import ParkingQUBO
from algorithms.quantum_annealing import AnnealingOptimizer
from constraints.physical_constraints import ConstraintManager
from visualization.plot_layout import ParkingLayoutVisualizer
from utils.performance_metrics import MetricsCalculator, BenchmarkComparator


class ParkingOptimizer:
    """
    Main class that orchestrates the parking optimization process.
    """
    
    def __init__(self, config_path: str = "config.yaml"):
        """
        Initialize the parking optimizer.
        
        Args:
            config_path: Path to configuration file
        """
        self.config = self._load_config(config_path)
        self.field = None
        self.qubo_model = None
        self.optimizer = None
        self.visualizer = None
        self.metrics_calculator = None
        
        self._setup_components()
    
    def _load_config(self, config_path: str) -> Dict[str, Any]:
        """Load configuration from YAML file."""
        try:
            with open(config_path, 'r') as f:
                config = yaml.safe_load(f)
            print(f"Configuration loaded from {config_path}")
            return config
        except FileNotFoundError:
            print(f"Configuration file {config_path} not found. Using default configuration.")
            return self._get_default_config()
    
    def _get_default_config(self) -> Dict[str, Any]:
        """Get default configuration if config file is not found."""
        return {
            'field': {'width': 100.0, 'height': 60.0, 'resolution': 0.5},
            'entrances': [{'location': [0, 30], 'direction': 'east'}, 
                         {'location': [100, 30], 'direction': 'west'}],
            'vehicle': {'length': 4.5, 'width': 2.0, 'turning_radius': 6.0},
            'spacing': {'between_cars': 0.5, 'from_road': 1.0, 'maneuver_space': 2.5},
            'roads': {'width': 6.0, 'min_length': 10.0, 'intersection_radius': 8.0},
            'optimization': {'algorithm': 'simulated_annealing', 'max_iterations': 10000,
                           'initial_temperature': 100.0, 'cooling_rate': 0.95, 'min_temperature': 0.01},
            'weights': {'parking_capacity': 1.0, 'walking_distance': 0.3, 
                       'traffic_flow': 0.2, 'space_utilization': 0.1}
        }
    
    def _setup_components(self):
        """Initialize all system components."""
        # Initialize field
        field_config = self.config['field']
        self.field = ParkingField(
            field_config['width'], 
            field_config['height'], 
            field_config['resolution']
        )
        
        # Add entrances
        for entrance in self.config['entrances']:
            self.field.add_entrance(entrance['location'][0], entrance['location'][1])
        
        # Add basic road network connecting entrances
        self._create_basic_road_network()
        
        # Initialize QUBO model
        self.qubo_model = ParkingQUBO(
            field_config['width'],
            field_config['height'],
            field_config['resolution'],
            self.config['vehicle']['length'],
            self.config['vehicle']['width'],
            self.config['roads']['width'],
            [tuple(e['location']) for e in self.config['entrances']],
            self.config['weights']
        )
        
        # Initialize optimizer
        opt_config = self.config['optimization']
        self.optimizer = AnnealingOptimizer(
            algorithm=opt_config['algorithm'],
            max_iterations=opt_config.get('max_iterations', 10000),
            initial_temperature=opt_config.get('initial_temperature', 100.0),
            cooling_rate=opt_config.get('cooling_rate', 0.95),
            min_temperature=opt_config.get('min_temperature', 0.01)
        )
        
        # Initialize visualizer and metrics calculator
        self.visualizer = ParkingLayoutVisualizer(self.field)
        self.metrics_calculator = MetricsCalculator(self.field, self.config)
        
        print("All components initialized successfully.")
    
    def _create_basic_road_network(self):
        """Create a basic road network connecting the entrances."""
        entrances = [tuple(e['location']) for e in self.config['entrances']]
        
        if len(entrances) >= 2:
            # Create a simple road connecting the two entrances
            start_x, start_y = entrances[0]
            end_x, end_y = entrances[1]
            
            # Add horizontal road if entrances are on opposite sides
            if abs(start_x - end_x) > abs(start_y - end_y):
                # Horizontal connection
                mid_y = (start_y + end_y) / 2
                self.field.add_road_segment(start_x, start_y, start_x + 20, mid_y)
                self.field.add_road_segment(start_x + 20, mid_y, end_x - 20, mid_y)
                self.field.add_road_segment(end_x - 20, mid_y, end_x, end_y)
            else:
                # Vertical connection
                mid_x = (start_x + end_x) / 2
                self.field.add_road_segment(start_x, start_y, mid_x, start_y + 20)
                self.field.add_road_segment(mid_x, start_y + 20, mid_x, end_y - 20)
                self.field.add_road_segment(mid_x, end_y - 20, end_x, end_y)
    
    def optimize(self, save_results: bool = True, output_dir: str = "results") -> Dict[str, Any]:
        """
        Run the optimization process.
        
        Args:
            save_results: Whether to save results to files
            output_dir: Directory to save results
            
        Returns:
            Dictionary containing optimization results and metrics
        """
        print("Starting parking space optimization...")
        
        # Build QUBO matrix
        print("Building QUBO formulation...")
        Q = self.qubo_model.build_qubo_matrix()
        print(f"QUBO matrix size: {Q.shape[0]} x {Q.shape[1]}")
        print(f"Non-zero elements: {np.count_nonzero(Q)}")
        
        # Run optimization
        print("Running quantum annealing optimization...")
        optimization_result = self.optimizer.optimize(Q)
        
        # Get variable mapping
        variable_mapping = self.qubo_model.get_variable_mapping()
        
        # Calculate performance metrics
        print("Calculating performance metrics...")
        metrics = self.metrics_calculator.calculate_metrics(
            optimization_result['solution'], 
            variable_mapping, 
            optimization_result
        )
        
        # Apply solution to field
        self._apply_solution_to_field(optimization_result['solution'], variable_mapping)
        
        # Prepare results
        results = {
            'optimization_result': optimization_result,
            'metrics': metrics,
            'variable_mapping': variable_mapping,
            'field_config': {
                'width': self.field.width,
                'height': self.field.height,
                'resolution': self.field.resolution
            }
        }
        
        # Save results if requested
        if save_results:
            self._save_results(results, output_dir)
        
        # Print summary
        self._print_summary(metrics)
        
        return results
    
    def _apply_solution_to_field(self, solution: np.ndarray, 
                               variable_mapping: Dict[int, tuple]):
        """Apply the optimization solution to the field representation."""
        # Clear existing parking spaces
        from models.field_representation import CellType
        for i in range(self.field.grid_width):
            for j in range(self.field.grid_height):
                if self.field.grid[i, j] == CellType.PARKING:
                    self.field.grid[i, j] = CellType.EMPTY
        
        self.field.parking_spaces.clear()
        
        # Apply solution
        for var_idx, (var_type, i, j) in variable_mapping.items():
            if var_idx < len(solution) and solution[var_idx] == 1:
                if var_type == 'parking':
                    x, y = self.field.grid_to_world(i, j)
                    self.field.place_parking_space(x, y)
    
    def _save_results(self, results: Dict[str, Any], output_dir: str):
        """Save optimization results to files."""
        os.makedirs(output_dir, exist_ok=True)
        
        # Save metrics as JSON
        metrics_dict = {
            'total_parking_spaces': results['metrics'].total_parking_spaces,
            'parking_density': results['metrics'].parking_density,
            'space_utilization': results['metrics'].space_utilization,
            'avg_walking_distance': results['metrics'].avg_walking_distance,
            'max_walking_distance': results['metrics'].max_walking_distance,
            'accessibility_score': results['metrics'].accessibility_score,
            'traffic_flow_score': results['metrics'].traffic_flow_score,
            'spacing_compliance': results['metrics'].spacing_compliance,
            'constraint_violations': results['metrics'].constraint_violations,
            'solve_time': results['metrics'].solve_time,
            'energy_value': results['metrics'].energy_value,
            'overall_score': results['metrics'].overall_score
        }
        
        with open(os.path.join(output_dir, 'metrics.json'), 'w') as f:
            json.dump(metrics_dict, f, indent=2)
        
        # Save field configuration
        self.field.save_configuration(os.path.join(output_dir, 'field_layout.yaml'))
        
        # Generate and save visualizations
        self._generate_visualizations(results, output_dir)
        
        print(f"Results saved to {output_dir}/")
    
    def _generate_visualizations(self, results: Dict[str, Any], output_dir: str):
        """Generate and save visualization plots."""
        # Field layout plot
        fig1 = self.visualizer.plot_field_layout(
            title="Optimized Parking Layout",
            save_path=os.path.join(output_dir, 'parking_layout.png')
        )
        plt.close(fig1)
        
        # Optimization progress plot
        fig2 = self.visualizer.plot_optimization_progress(
            results['optimization_result'],
            save_path=os.path.join(output_dir, 'optimization_progress.png')
        )
        plt.close(fig2)
        
        # Solution analysis plot
        fig3 = self.visualizer.plot_solution_analysis(
            results['optimization_result']['solution'],
            results['variable_mapping'],
            save_path=os.path.join(output_dir, 'solution_analysis.png')
        )
        plt.close(fig3)
        
        print("Visualizations saved.")
    
    def _print_summary(self, metrics):
        """Print optimization summary."""
        print("\n" + "="*60)
        print("OPTIMIZATION SUMMARY")
        print("="*60)
        print(f"Total Parking Spaces: {metrics.total_parking_spaces}")
        print(f"Space Utilization: {metrics.space_utilization:.1f}%")
        print(f"Average Walking Distance: {metrics.avg_walking_distance:.1f}m")
        print(f"Accessibility Score: {metrics.accessibility_score:.1f}%")
        print(f"Traffic Flow Score: {metrics.traffic_flow_score:.1f}")
        print(f"Spacing Compliance: {metrics.spacing_compliance:.1f}%")
        print(f"Constraint Violations: {metrics.constraint_violations}")
        print(f"Solve Time: {metrics.solve_time:.2f}s")
        print(f"Overall Score: {metrics.overall_score:.1f}/100")
        print("="*60)
    
    def compare_algorithms(self, algorithms: list, output_dir: str = "comparison") -> Dict[str, Any]:
        """
        Compare different optimization algorithms.
        
        Args:
            algorithms: List of algorithm names to compare
            output_dir: Directory to save comparison results
            
        Returns:
            Comparison results
        """
        print("Starting algorithm comparison...")
        
        comparator = BenchmarkComparator()
        Q = self.qubo_model.build_qubo_matrix()
        variable_mapping = self.qubo_model.get_variable_mapping()
        
        for algorithm in algorithms:
            print(f"\nTesting algorithm: {algorithm}")
            
            # Create optimizer for this algorithm
            optimizer = AnnealingOptimizer(algorithm=algorithm)
            
            # Run optimization
            result = optimizer.optimize(Q)
            
            # Calculate metrics
            metrics = self.metrics_calculator.calculate_metrics(
                result['solution'], variable_mapping, result
            )
            
            # Add to comparison
            comparator.add_result(algorithm, metrics, {'algorithm': algorithm})
        
        # Generate comparison report
        comparison_report = comparator.generate_comparison_report()
        
        # Save comparison results
        os.makedirs(output_dir, exist_ok=True)
        with open(os.path.join(output_dir, 'algorithm_comparison.json'), 'w') as f:
            json.dump(comparison_report, f, indent=2, default=str)
        
        print(f"\nComparison results saved to {output_dir}/")
        return comparison_report


def main():
    """Main function for command-line interface."""
    parser = argparse.ArgumentParser(description='Quantum Annealing Parking Optimization')
    parser.add_argument('--config', default='config.yaml', help='Configuration file path')
    parser.add_argument('--output', default='results', help='Output directory')
    parser.add_argument('--algorithm', default='simulated_annealing', 
                       choices=['simulated_annealing', 'quantum_annealing', 'hybrid'],
                       help='Optimization algorithm')
    parser.add_argument('--compare', action='store_true', 
                       help='Compare multiple algorithms')
    parser.add_argument('--field-width', type=float, help='Override field width')
    parser.add_argument('--field-height', type=float, help='Override field height')
    
    args = parser.parse_args()
    
    # Initialize optimizer
    optimizer = ParkingOptimizer(args.config)
    
    # Override field dimensions if specified
    if args.field_width:
        optimizer.config['field']['width'] = args.field_width
    if args.field_height:
        optimizer.config['field']['height'] = args.field_height
        
    # Re-setup components with new config
    if args.field_width or args.field_height:
        optimizer._setup_components()
    
    # Override algorithm
    optimizer.config['optimization']['algorithm'] = args.algorithm
    optimizer.optimizer = AnnealingOptimizer(algorithm=args.algorithm)
    
    if args.compare:
        # Compare algorithms
        algorithms = ['simulated_annealing']
        if optimizer.optimizer.is_dwave_available():
            algorithms.extend(['quantum_annealing', 'hybrid'])
        
        comparison_results = optimizer.compare_algorithms(algorithms, args.output)
        print("\nAlgorithm Comparison Complete!")
        
        # Print recommendations
        for rec in comparison_results.get('recommendations', []):
            print(f"• {rec}")
    else:
        # Single optimization run
        results = optimizer.optimize(save_results=True, output_dir=args.output)
        print(f"\nOptimization complete! Results saved to {args.output}/")


if __name__ == "__main__":
    main()
