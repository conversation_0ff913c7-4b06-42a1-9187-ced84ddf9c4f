"""
Performance Metrics Module

This module provides comprehensive performance evaluation metrics for
parking optimization solutions, including capacity, efficiency, and quality measures.
"""

import numpy as np
from typing import Dict, List, Tuple, Any, Optional
import time
from dataclasses import dataclass
from models.field_representation import ParkingField, CellType


@dataclass
class PerformanceMetrics:
    """Data class to store performance metrics."""
    # Capacity metrics
    total_parking_spaces: int
    parking_density: float
    space_utilization: float
    
    # Efficiency metrics
    avg_walking_distance: float
    max_walking_distance: float
    accessibility_score: float
    
    # Quality metrics
    traffic_flow_score: float
    spacing_compliance: float
    constraint_violations: int
    
    # Optimization metrics
    solve_time: float
    energy_value: float
    convergence_iterations: int
    
    # Overall score
    overall_score: float


class MetricsCalculator:
    """
    Calculates comprehensive performance metrics for parking solutions.
    """
    
    def __init__(self, field: ParkingField, config: Dict[str, Any]):
        """
        Initialize metrics calculator.
        
        Args:
            field: ParkingField instance
            config: Configuration dictionary
        """
        self.field = field
        self.config = config
        self.car_length = config['vehicle']['length']
        self.car_width = config['vehicle']['width']
        self.min_spacing = config['spacing']['between_cars']
    
    def calculate_metrics(self, solution: np.ndarray, 
                         variable_mapping: Dict[int, Tuple[str, int, int]],
                         optimization_result: Dict[str, Any]) -> PerformanceMetrics:
        """
        Calculate comprehensive performance metrics for a solution.
        
        Args:
            solution: Binary solution vector
            variable_mapping: Mapping from variable index to (type, i, j)
            optimization_result: Result from optimization algorithm
            
        Returns:
            PerformanceMetrics object with all calculated metrics
        """
        # Extract parking positions from solution
        parking_positions = self._extract_parking_positions(solution, variable_mapping)
        
        # Calculate capacity metrics
        capacity_metrics = self._calculate_capacity_metrics(parking_positions)
        
        # Calculate efficiency metrics
        efficiency_metrics = self._calculate_efficiency_metrics(parking_positions)
        
        # Calculate quality metrics
        quality_metrics = self._calculate_quality_metrics(parking_positions)
        
        # Calculate optimization metrics
        opt_metrics = self._calculate_optimization_metrics(optimization_result)
        
        # Calculate overall score
        overall_score = self._calculate_overall_score(
            capacity_metrics, efficiency_metrics, quality_metrics
        )
        
        return PerformanceMetrics(
            # Capacity
            total_parking_spaces=capacity_metrics['total_spaces'],
            parking_density=capacity_metrics['density'],
            space_utilization=capacity_metrics['utilization'],
            
            # Efficiency
            avg_walking_distance=efficiency_metrics['avg_walking_distance'],
            max_walking_distance=efficiency_metrics['max_walking_distance'],
            accessibility_score=efficiency_metrics['accessibility_score'],
            
            # Quality
            traffic_flow_score=quality_metrics['traffic_flow_score'],
            spacing_compliance=quality_metrics['spacing_compliance'],
            constraint_violations=quality_metrics['constraint_violations'],
            
            # Optimization
            solve_time=opt_metrics['solve_time'],
            energy_value=opt_metrics['energy_value'],
            convergence_iterations=opt_metrics['convergence_iterations'],
            
            # Overall
            overall_score=overall_score
        )
    
    def _extract_parking_positions(self, solution: np.ndarray, 
                                 variable_mapping: Dict[int, Tuple[str, int, int]]) -> List[Tuple[int, int]]:
        """Extract parking positions from solution vector."""
        parking_positions = []
        
        for var_idx, (var_type, i, j) in variable_mapping.items():
            if var_idx < len(solution) and solution[var_idx] == 1 and var_type == 'parking':
                parking_positions.append((i, j))
        
        return parking_positions
    
    def _calculate_capacity_metrics(self, parking_positions: List[Tuple[int, int]]) -> Dict[str, float]:
        """Calculate capacity-related metrics."""
        total_spaces = len(parking_positions)
        
        # Calculate theoretical maximum capacity
        field_area = self.field.width * self.field.height
        car_area = self.car_length * self.car_width
        theoretical_max = int(field_area / (car_area * 1.5))  # Factor for roads and spacing
        
        # Calculate actual density and utilization
        density = total_spaces / field_area if field_area > 0 else 0
        utilization = (total_spaces / theoretical_max * 100) if theoretical_max > 0 else 0
        
        return {
            'total_spaces': total_spaces,
            'density': density,
            'utilization': min(utilization, 100.0),
            'theoretical_max': theoretical_max
        }
    
    def _calculate_efficiency_metrics(self, parking_positions: List[Tuple[int, int]]) -> Dict[str, float]:
        """Calculate efficiency-related metrics."""
        if not parking_positions:
            return {
                'avg_walking_distance': float('inf'),
                'max_walking_distance': float('inf'),
                'accessibility_score': 0.0
            }
        
        walking_distances = []
        accessible_count = 0
        
        for park_i, park_j in parking_positions:
            # Calculate walking distance to nearest entrance
            walking_dist = self.field.get_walking_distance(park_i, park_j)
            walking_distances.append(walking_dist)
            
            # Check accessibility
            if self.field.is_accessible(park_i, park_j):
                accessible_count += 1
        
        avg_walking_distance = np.mean(walking_distances)
        max_walking_distance = np.max(walking_distances)
        accessibility_score = (accessible_count / len(parking_positions)) * 100
        
        return {
            'avg_walking_distance': avg_walking_distance,
            'max_walking_distance': max_walking_distance,
            'accessibility_score': accessibility_score
        }
    
    def _calculate_quality_metrics(self, parking_positions: List[Tuple[int, int]]) -> Dict[str, float]:
        """Calculate quality-related metrics."""
        # Traffic flow score (simplified heuristic)
        traffic_flow_score = self._calculate_traffic_flow_score(parking_positions)
        
        # Spacing compliance
        spacing_compliance = self._calculate_spacing_compliance(parking_positions)
        
        # Constraint violations
        constraint_violations = self._count_constraint_violations(parking_positions)
        
        return {
            'traffic_flow_score': traffic_flow_score,
            'spacing_compliance': spacing_compliance,
            'constraint_violations': constraint_violations
        }
    
    def _calculate_optimization_metrics(self, optimization_result: Dict[str, Any]) -> Dict[str, float]:
        """Calculate optimization-related metrics."""
        solve_time = optimization_result.get('solve_time', 0.0)
        if 'total_solve_time' in optimization_result:
            solve_time = optimization_result['total_solve_time']
        
        energy_value = optimization_result.get('energy', 0.0)
        
        convergence_iterations = optimization_result.get('iterations', 0)
        if 'sa_result' in optimization_result:
            convergence_iterations = optimization_result['sa_result'].get('iterations', 0)
        
        return {
            'solve_time': solve_time,
            'energy_value': energy_value,
            'convergence_iterations': convergence_iterations
        }
    
    def _calculate_traffic_flow_score(self, parking_positions: List[Tuple[int, int]]) -> float:
        """Calculate traffic flow efficiency score."""
        if not parking_positions or not self.field.entrances:
            return 0.0
        
        # Simple heuristic: penalize parking spaces that block direct paths between entrances
        score = 100.0
        
        for park_i, park_j in parking_positions:
            for i, (ent1_x, ent1_y) in enumerate(self.field.entrances):
                for j, (ent2_x, ent2_y) in enumerate(self.field.entrances[i+1:], i+1):
                    # Calculate distance from parking space to line between entrances
                    dist_to_line = self._point_to_line_distance(
                        park_i, park_j, ent1_x, ent1_y, ent2_x, ent2_y
                    )
                    
                    # Penalize if too close to main traffic corridor
                    if dist_to_line < 10:  # Within 10 grid cells
                        score -= 5.0 / (dist_to_line + 1)
        
        return max(0.0, score)
    
    def _calculate_spacing_compliance(self, parking_positions: List[Tuple[int, int]]) -> float:
        """Calculate percentage of parking spaces that comply with spacing requirements."""
        if len(parking_positions) <= 1:
            return 100.0
        
        compliant_count = 0
        total_pairs = 0
        
        min_spacing_cells = int(self.min_spacing / self.field.resolution)
        
        for i, (x1, y1) in enumerate(parking_positions):
            for j, (x2, y2) in enumerate(parking_positions[i+1:], i+1):
                total_pairs += 1
                distance = np.sqrt((x1 - x2)**2 + (y1 - y2)**2)
                
                if distance >= min_spacing_cells:
                    compliant_count += 1
        
        return (compliant_count / total_pairs * 100) if total_pairs > 0 else 100.0
    
    def _count_constraint_violations(self, parking_positions: List[Tuple[int, int]]) -> int:
        """Count the number of constraint violations."""
        violations = 0
        
        for park_i, park_j in parking_positions:
            # Check boundary constraints
            if not self._is_within_boundaries(park_i, park_j):
                violations += 1
            
            # Check accessibility constraints
            if not self.field.is_accessible(park_i, park_j):
                violations += 1
        
        return violations
    
    def _calculate_overall_score(self, capacity_metrics: Dict[str, float],
                               efficiency_metrics: Dict[str, float],
                               quality_metrics: Dict[str, float]) -> float:
        """Calculate overall performance score (0-100)."""
        # Weighted combination of different metric categories
        weights = self.config.get('weights', {
            'parking_capacity': 0.4,
            'walking_distance': 0.3,
            'traffic_flow': 0.2,
            'space_utilization': 0.1
        })
        
        # Normalize metrics to 0-100 scale
        capacity_score = min(capacity_metrics['utilization'], 100.0)
        
        # Efficiency score (inverse of walking distance, normalized)
        max_possible_distance = np.sqrt(self.field.width**2 + self.field.height**2)
        efficiency_score = max(0, 100 - (efficiency_metrics['avg_walking_distance'] / max_possible_distance * 100))
        
        # Quality score
        quality_score = (quality_metrics['traffic_flow_score'] + quality_metrics['spacing_compliance']) / 2
        
        # Space utilization score
        utilization_score = capacity_metrics['utilization']
        
        # Weighted average
        overall_score = (
            weights.get('parking_capacity', 0.4) * capacity_score +
            weights.get('walking_distance', 0.3) * efficiency_score +
            weights.get('traffic_flow', 0.2) * quality_score +
            weights.get('space_utilization', 0.1) * utilization_score
        )
        
        return min(100.0, max(0.0, overall_score))
    
    def _point_to_line_distance(self, px: int, py: int, x1: int, y1: int, x2: int, y2: int) -> float:
        """Calculate distance from point to line segment."""
        dx_p = px - x1
        dy_p = py - y1
        dx_l = x2 - x1
        dy_l = y2 - y1
        
        line_length_sq = dx_l**2 + dy_l**2
        
        if line_length_sq == 0:
            return np.sqrt(dx_p**2 + dy_p**2)
        
        t = max(0, min(1, (dx_p * dx_l + dy_p * dy_l) / line_length_sq))
        closest_x = x1 + t * dx_l
        closest_y = y1 + t * dy_l
        
        return np.sqrt((px - closest_x)**2 + (py - closest_y)**2)
    
    def _is_within_boundaries(self, park_i: int, park_j: int) -> bool:
        """Check if parking space is within field boundaries."""
        car_length_cells = int(self.car_length / self.field.resolution)
        car_width_cells = int(self.car_width / self.field.resolution)
        
        return (car_length_cells//2 <= park_i < self.field.grid_width - car_length_cells//2 and
                car_width_cells//2 <= park_j < self.field.grid_height - car_width_cells//2)


class BenchmarkComparator:
    """
    Compares different optimization solutions and algorithms.
    """
    
    def __init__(self):
        """Initialize benchmark comparator."""
        self.results = []
    
    def add_result(self, name: str, metrics: PerformanceMetrics, 
                  additional_info: Optional[Dict[str, Any]] = None):
        """Add a result to the benchmark comparison."""
        result = {
            'name': name,
            'metrics': metrics,
            'additional_info': additional_info or {}
        }
        self.results.append(result)
    
    def generate_comparison_report(self) -> Dict[str, Any]:
        """Generate a comprehensive comparison report."""
        if not self.results:
            return {'error': 'No results to compare'}
        
        report = {
            'summary': self._generate_summary(),
            'detailed_comparison': self._generate_detailed_comparison(),
            'rankings': self._generate_rankings(),
            'recommendations': self._generate_recommendations()
        }
        
        return report
    
    def _generate_summary(self) -> Dict[str, Any]:
        """Generate summary statistics."""
        metrics_list = [result['metrics'] for result in self.results]
        
        return {
            'total_algorithms': len(self.results),
            'best_overall_score': max(m.overall_score for m in metrics_list),
            'avg_solve_time': np.mean([m.solve_time for m in metrics_list]),
            'max_parking_spaces': max(m.total_parking_spaces for m in metrics_list)
        }
    
    def _generate_detailed_comparison(self) -> List[Dict[str, Any]]:
        """Generate detailed comparison of all results."""
        comparison = []
        
        for result in self.results:
            metrics = result['metrics']
            comparison.append({
                'name': result['name'],
                'overall_score': metrics.overall_score,
                'parking_spaces': metrics.total_parking_spaces,
                'avg_walking_distance': metrics.avg_walking_distance,
                'solve_time': metrics.solve_time,
                'accessibility_score': metrics.accessibility_score,
                'traffic_flow_score': metrics.traffic_flow_score
            })
        
        return comparison
    
    def _generate_rankings(self) -> Dict[str, List[str]]:
        """Generate rankings for different criteria."""
        rankings = {}
        
        # Overall score ranking
        rankings['overall_score'] = sorted(
            [r['name'] for r in self.results],
            key=lambda name: next(r['metrics'].overall_score for r in self.results if r['name'] == name),
            reverse=True
        )
        
        # Parking capacity ranking
        rankings['parking_capacity'] = sorted(
            [r['name'] for r in self.results],
            key=lambda name: next(r['metrics'].total_parking_spaces for r in self.results if r['name'] == name),
            reverse=True
        )
        
        # Efficiency ranking (lower walking distance is better)
        rankings['efficiency'] = sorted(
            [r['name'] for r in self.results],
            key=lambda name: next(r['metrics'].avg_walking_distance for r in self.results if r['name'] == name)
        )
        
        # Speed ranking (lower solve time is better)
        rankings['speed'] = sorted(
            [r['name'] for r in self.results],
            key=lambda name: next(r['metrics'].solve_time for r in self.results if r['name'] == name)
        )
        
        return rankings
    
    def _generate_recommendations(self) -> List[str]:
        """Generate recommendations based on the comparison."""
        recommendations = []
        
        if not self.results:
            return recommendations
        
        # Find best overall algorithm
        best_overall = max(self.results, key=lambda r: r['metrics'].overall_score)
        recommendations.append(f"Best overall algorithm: {best_overall['name']} (score: {best_overall['metrics'].overall_score:.2f})")
        
        # Find fastest algorithm
        fastest = min(self.results, key=lambda r: r['metrics'].solve_time)
        recommendations.append(f"Fastest algorithm: {fastest['name']} ({fastest['metrics'].solve_time:.2f}s)")
        
        # Find most space-efficient
        most_spaces = max(self.results, key=lambda r: r['metrics'].total_parking_spaces)
        recommendations.append(f"Highest capacity: {most_spaces['name']} ({most_spaces['metrics'].total_parking_spaces} spaces)")
        
        return recommendations
