"""
Quantum Annealing Algorithms for Parking Optimization

This module implements quantum annealing algorithms including simulated annealing
and D-Wave quantum annealer integration for solving the parking optimization QUBO.
"""

import numpy as np
from typing import Dict, List, Tuple, Optional, Any
import random
import time
from abc import ABC, abstractmethod

try:
    import dimod
    from dwave.system import DWaveSampler, EmbeddingComposite
    DWAVE_AVAILABLE = True
except ImportError:
    DWAVE_AVAILABLE = False


class QuantumAnnealerBase(ABC):
    """Base class for quantum annealing algorithms."""
    
    @abstractmethod
    def solve(self, Q: np.ndarray, **kwargs) -> Dict[str, Any]:
        """
        Solve the QUBO problem.
        
        Args:
            Q: QUBO matrix
            **kwargs: Algorithm-specific parameters
            
        Returns:
            Dictionary containing solution and metadata
        """
        pass


class SimulatedAnnealer(QuantumAnnealerBase):
    """
    Simulated Annealing implementation for QUBO problems.
    """
    
    def __init__(self, initial_temperature: float = 100.0, 
                 cooling_rate: float = 0.95, min_temperature: float = 0.01,
                 max_iterations: int = 10000):
        """
        Initialize simulated annealer.
        
        Args:
            initial_temperature: Starting temperature
            cooling_rate: Rate at which temperature decreases
            min_temperature: Minimum temperature before stopping
            max_iterations: Maximum number of iterations
        """
        self.initial_temperature = initial_temperature
        self.cooling_rate = cooling_rate
        self.min_temperature = min_temperature
        self.max_iterations = max_iterations
    
    def solve(self, Q: np.ndarray, **kwargs) -> Dict[str, Any]:
        """
        Solve QUBO using simulated annealing.
        
        Args:
            Q: QUBO matrix (n x n)
            **kwargs: Additional parameters
            
        Returns:
            Dictionary with solution, energy, and metadata
        """
        n = Q.shape[0]
        
        # Initialize random solution
        current_solution = np.random.randint(0, 2, n)
        current_energy = self._calculate_energy(Q, current_solution)
        
        best_solution = current_solution.copy()
        best_energy = current_energy
        
        temperature = self.initial_temperature
        iteration = 0
        
        energy_history = [current_energy]
        temperature_history = [temperature]
        
        start_time = time.time()
        
        while temperature > self.min_temperature and iteration < self.max_iterations:
            # Generate neighbor solution by flipping a random bit
            neighbor_solution = current_solution.copy()
            flip_index = random.randint(0, n - 1)
            neighbor_solution[flip_index] = 1 - neighbor_solution[flip_index]
            
            neighbor_energy = self._calculate_energy(Q, neighbor_solution)
            energy_diff = neighbor_energy - current_energy
            
            # Accept or reject the neighbor solution
            if energy_diff < 0 or random.random() < np.exp(-energy_diff / temperature):
                current_solution = neighbor_solution
                current_energy = neighbor_energy
                
                # Update best solution if improved
                if current_energy < best_energy:
                    best_solution = current_solution.copy()
                    best_energy = current_energy
            
            # Cool down
            temperature *= self.cooling_rate
            iteration += 1
            
            # Record history
            if iteration % 100 == 0:
                energy_history.append(current_energy)
                temperature_history.append(temperature)
        
        solve_time = time.time() - start_time
        
        return {
            'solution': best_solution,
            'energy': best_energy,
            'iterations': iteration,
            'solve_time': solve_time,
            'energy_history': energy_history,
            'temperature_history': temperature_history,
            'final_temperature': temperature
        }
    
    def _calculate_energy(self, Q: np.ndarray, x: np.ndarray) -> float:
        """Calculate QUBO energy: x^T Q x"""
        return float(x.T @ Q @ x)


class DWaveAnnealer(QuantumAnnealerBase):
    """
    D-Wave quantum annealer implementation.
    """
    
    def __init__(self, num_reads: int = 1000, annealing_time: int = 20,
                 chain_strength: float = 1.0):
        """
        Initialize D-Wave annealer.
        
        Args:
            num_reads: Number of annealing runs
            annealing_time: Annealing time in microseconds
            chain_strength: Strength of chains in embedding
        """
        if not DWAVE_AVAILABLE:
            raise ImportError("D-Wave Ocean SDK not available. Install with: pip install dwave-ocean-sdk")
        
        self.num_reads = num_reads
        self.annealing_time = annealing_time
        self.chain_strength = chain_strength
        
        # Initialize D-Wave sampler
        try:
            self.sampler = EmbeddingComposite(DWaveSampler())
        except Exception as e:
            print(f"Warning: Could not connect to D-Wave system: {e}")
            self.sampler = None
    
    def solve(self, Q: np.ndarray, **kwargs) -> Dict[str, Any]:
        """
        Solve QUBO using D-Wave quantum annealer.
        
        Args:
            Q: QUBO matrix
            **kwargs: Additional D-Wave parameters
            
        Returns:
            Dictionary with solution, energy, and metadata
        """
        if self.sampler is None:
            raise RuntimeError("D-Wave sampler not available")
        
        # Convert numpy array to dictionary format for D-Wave
        qubo_dict = {}
        n = Q.shape[0]
        
        for i in range(n):
            for j in range(i, n):
                if Q[i, j] != 0:
                    qubo_dict[(i, j)] = Q[i, j]
        
        start_time = time.time()
        
        # Submit to D-Wave
        response = self.sampler.sample_qubo(
            qubo_dict,
            num_reads=self.num_reads,
            annealing_time=self.annealing_time,
            chain_strength=self.chain_strength,
            **kwargs
        )
        
        solve_time = time.time() - start_time
        
        # Extract best solution
        best_sample = response.first.sample
        best_energy = response.first.energy
        
        # Convert solution back to numpy array
        solution = np.zeros(n, dtype=int)
        for i in range(n):
            solution[i] = best_sample.get(i, 0)
        
        return {
            'solution': solution,
            'energy': best_energy,
            'num_reads': self.num_reads,
            'solve_time': solve_time,
            'response': response,
            'chain_break_fraction': response.data_vectors['chain_break_fraction'].mean(),
            'timing': response.info.get('timing', {})
        }


class HybridAnnealer(QuantumAnnealerBase):
    """
    Hybrid annealer that combines simulated annealing with quantum annealing.
    """
    
    def __init__(self, use_dwave: bool = True, sa_iterations: int = 5000,
                 dwave_params: Optional[Dict] = None):
        """
        Initialize hybrid annealer.
        
        Args:
            use_dwave: Whether to use D-Wave for final optimization
            sa_iterations: Number of simulated annealing iterations
            dwave_params: Parameters for D-Wave annealer
        """
        self.use_dwave = use_dwave and DWAVE_AVAILABLE
        self.sa_iterations = sa_iterations
        
        # Initialize component annealers
        self.sa_annealer = SimulatedAnnealer(max_iterations=sa_iterations)
        
        if self.use_dwave:
            dwave_params = dwave_params or {}
            self.dwave_annealer = DWaveAnnealer(**dwave_params)
        else:
            self.dwave_annealer = None
    
    def solve(self, Q: np.ndarray, **kwargs) -> Dict[str, Any]:
        """
        Solve QUBO using hybrid approach.
        
        Args:
            Q: QUBO matrix
            **kwargs: Additional parameters
            
        Returns:
            Dictionary with solution, energy, and metadata
        """
        start_time = time.time()
        
        # Phase 1: Simulated annealing for initial optimization
        print("Phase 1: Simulated Annealing...")
        sa_result = self.sa_annealer.solve(Q, **kwargs)
        
        best_solution = sa_result['solution']
        best_energy = sa_result['energy']
        
        results = {
            'sa_result': sa_result,
            'solution': best_solution,
            'energy': best_energy
        }
        
        # Phase 2: D-Wave quantum annealing for refinement (if available)
        if self.use_dwave and self.dwave_annealer is not None:
            try:
                print("Phase 2: D-Wave Quantum Annealing...")
                dwave_result = self.dwave_annealer.solve(Q, **kwargs)
                
                # Use D-Wave result if it's better
                if dwave_result['energy'] < best_energy:
                    best_solution = dwave_result['solution']
                    best_energy = dwave_result['energy']
                
                results['dwave_result'] = dwave_result
                results['used_dwave'] = True
                
            except Exception as e:
                print(f"D-Wave optimization failed: {e}")
                results['dwave_error'] = str(e)
                results['used_dwave'] = False
        else:
            results['used_dwave'] = False
        
        total_time = time.time() - start_time
        
        results.update({
            'solution': best_solution,
            'energy': best_energy,
            'total_solve_time': total_time
        })
        
        return results


class AnnealingOptimizer:
    """
    Main optimizer class that manages different annealing algorithms.
    """
    
    def __init__(self, algorithm: str = "simulated_annealing", **params):
        """
        Initialize optimizer.
        
        Args:
            algorithm: Type of annealing algorithm to use
            **params: Algorithm-specific parameters
        """
        self.algorithm = algorithm
        self.params = params
        
        if algorithm == "simulated_annealing":
            self.annealer = SimulatedAnnealer(**params)
        elif algorithm == "quantum_annealing" and DWAVE_AVAILABLE:
            self.annealer = DWaveAnnealer(**params)
        elif algorithm == "hybrid":
            self.annealer = HybridAnnealer(**params)
        else:
            # Fallback to simulated annealing
            print(f"Algorithm '{algorithm}' not available, using simulated annealing")
            self.annealer = SimulatedAnnealer(**params)
    
    def optimize(self, Q: np.ndarray, **kwargs) -> Dict[str, Any]:
        """
        Optimize the QUBO problem.
        
        Args:
            Q: QUBO matrix
            **kwargs: Additional optimization parameters
            
        Returns:
            Optimization results
        """
        print(f"Starting optimization with {self.algorithm}...")
        print(f"QUBO matrix size: {Q.shape}")
        print(f"Non-zero elements: {np.count_nonzero(Q)}")
        
        result = self.annealer.solve(Q, **kwargs)
        
        print(f"Optimization completed in {result.get('solve_time', 0):.2f} seconds")
        print(f"Best energy: {result['energy']:.4f}")
        
        return result
    
    def is_dwave_available(self) -> bool:
        """Check if D-Wave quantum annealer is available."""
        return DWAVE_AVAILABLE and hasattr(self, 'annealer') and isinstance(self.annealer, DWaveAnnealer)
