"""
Physical Constraints Module

This module implements various physical constraints for the parking optimization
problem, including spacing, accessibility, and traffic flow constraints.
"""

import numpy as np
from typing import List, Tuple, Dict, Set
from models.field_representation import Parking<PERSON>ield, CellType


class ConstraintValidator:
    """
    Validates physical constraints for parking space placement.
    """
    
    def __init__(self, field: ParkingField, car_length: float = 4.5, 
                 car_width: float = 2.0, turning_radius: float = 6.0):
        """
        Initialize constraint validator.
        
        Args:
            field: ParkingField instance
            car_length: Length of a car in meters
            car_width: Width of a car in meters
            turning_radius: Minimum turning radius in meters
        """
        self.field = field
        self.car_length = car_length
        self.car_width = car_width
        self.turning_radius = turning_radius
        
        # Convert to grid units
        self.car_length_cells = int(np.ceil(car_length / field.resolution))
        self.car_width_cells = int(np.ceil(car_width / field.resolution))
        self.turning_radius_cells = int(np.ceil(turning_radius / field.resolution))
    
    def validate_spacing_constraint(self, x1: int, y1: int, x2: int, y2: int,
                                  min_spacing: float = 0.5) -> bool:
        """
        Check if two parking spaces maintain minimum spacing.
        
        Args:
            x1, y1: Grid coordinates of first parking space
            x2, y2: Grid coordinates of second parking space
            min_spacing: Minimum spacing in meters
            
        Returns:
            True if spacing constraint is satisfied
        """
        min_spacing_cells = int(np.ceil(min_spacing / self.field.resolution))
        
        # Calculate distance between parking space centers
        distance = np.sqrt((x1 - x2)**2 + (y1 - y2)**2)
        
        # Account for car dimensions
        required_distance = (self.car_length_cells + self.car_width_cells) / 2 + min_spacing_cells
        
        return distance >= required_distance
    
    def validate_accessibility_constraint(self, parking_x: int, parking_y: int,
                                        max_access_distance: float = 10.0) -> bool:
        """
        Check if parking space is accessible from roads.
        
        Args:
            parking_x, parking_y: Grid coordinates of parking space
            max_access_distance: Maximum acceptable access distance in meters
            
        Returns:
            True if accessibility constraint is satisfied
        """
        max_dist_cells = int(max_access_distance / self.field.resolution)
        
        # Find nearest road
        min_distance = float('inf')
        for road_x, road_y in self.field.roads:
            distance = np.sqrt((parking_x - road_x)**2 + (parking_y - road_y)**2)
            min_distance = min(min_distance, distance)
        
        return min_distance <= max_dist_cells
    
    def validate_turning_radius_constraint(self, parking_x: int, parking_y: int) -> bool:
        """
        Check if parking space allows adequate turning radius for access.
        
        Args:
            parking_x, parking_y: Grid coordinates of parking space
            
        Returns:
            True if turning radius constraint is satisfied
        """
        # Check if there's enough clear space around parking for turning
        for dx in range(-self.turning_radius_cells, self.turning_radius_cells + 1):
            for dy in range(-self.turning_radius_cells, self.turning_radius_cells + 1):
                check_x, check_y = parking_x + dx, parking_y + dy
                
                if not self.field.is_valid_position(check_x, check_y):
                    continue
                
                # Distance from parking space center
                distance = np.sqrt(dx**2 + dy**2)
                
                if distance <= self.turning_radius_cells:
                    cell_type = self.field.grid[check_x, check_y]
                    # Must be empty, road, or the parking space itself
                    if cell_type not in [CellType.EMPTY, CellType.ROAD, CellType.PARKING]:
                        return False
        
        return True
    
    def validate_boundary_constraint(self, parking_x: int, parking_y: int) -> bool:
        """
        Check if parking space fits within field boundaries.
        
        Args:
            parking_x, parking_y: Grid coordinates of parking space center
            
        Returns:
            True if boundary constraint is satisfied
        """
        # Check all corners of the parking space
        half_length = self.car_length_cells // 2
        half_width = self.car_width_cells // 2
        
        corners = [
            (parking_x - half_length, parking_y - half_width),
            (parking_x + half_length, parking_y - half_width),
            (parking_x - half_length, parking_y + half_width),
            (parking_x + half_length, parking_y + half_width)
        ]
        
        for corner_x, corner_y in corners:
            if not self.field.is_valid_position(corner_x, corner_y):
                return False
        
        return True
    
    def validate_all_constraints(self, parking_x: int, parking_y: int,
                               existing_spaces: List[Tuple[int, int]]) -> Dict[str, bool]:
        """
        Validate all constraints for a parking space placement.
        
        Args:
            parking_x, parking_y: Grid coordinates of proposed parking space
            existing_spaces: List of existing parking space coordinates
            
        Returns:
            Dictionary with constraint validation results
        """
        results = {
            'boundary': self.validate_boundary_constraint(parking_x, parking_y),
            'accessibility': self.validate_accessibility_constraint(parking_x, parking_y),
            'turning_radius': self.validate_turning_radius_constraint(parking_x, parking_y),
            'spacing': True  # Will be updated below
        }
        
        # Check spacing against all existing spaces
        for existing_x, existing_y in existing_spaces:
            if not self.validate_spacing_constraint(parking_x, parking_y, 
                                                  existing_x, existing_y):
                results['spacing'] = False
                break
        
        return results


class TrafficFlowConstraints:
    """
    Implements constraints related to traffic flow optimization.
    """
    
    def __init__(self, field: ParkingField, road_width: float = 6.0):
        """
        Initialize traffic flow constraints.
        
        Args:
            field: ParkingField instance
            road_width: Width of roads in meters
        """
        self.field = field
        self.road_width = road_width
        self.road_width_cells = int(np.ceil(road_width / field.resolution))
    
    def calculate_traffic_flow_penalty(self, parking_x: int, parking_y: int) -> float:
        """
        Calculate penalty for parking space that interferes with traffic flow.
        
        Args:
            parking_x, parking_y: Grid coordinates of parking space
            
        Returns:
            Penalty value (higher = worse for traffic flow)
        """
        penalty = 0.0
        
        # Penalty for blocking main traffic corridors
        penalty += self._corridor_blocking_penalty(parking_x, parking_y)
        
        # Penalty for creating traffic bottlenecks
        penalty += self._bottleneck_penalty(parking_x, parking_y)
        
        # Penalty for interfering with entrance access
        penalty += self._entrance_access_penalty(parking_x, parking_y)
        
        return penalty
    
    def _corridor_blocking_penalty(self, parking_x: int, parking_y: int) -> float:
        """Calculate penalty for blocking main traffic corridors."""
        penalty = 0.0
        
        # Identify main corridors (straight lines between entrances)
        if len(self.field.entrances) >= 2:
            for i, (ent1_x, ent1_y) in enumerate(self.field.entrances):
                for j, (ent2_x, ent2_y) in enumerate(self.field.entrances[i+1:], i+1):
                    # Check if parking space is on the line between entrances
                    distance_to_line = self._point_to_line_distance(
                        parking_x, parking_y, ent1_x, ent1_y, ent2_x, ent2_y
                    )
                    
                    if distance_to_line < self.road_width_cells:
                        penalty += 100.0 / (distance_to_line + 1)
        
        return penalty
    
    def _bottleneck_penalty(self, parking_x: int, parking_y: int) -> float:
        """Calculate penalty for creating traffic bottlenecks."""
        penalty = 0.0
        
        # Count nearby roads and parking spaces
        nearby_roads = 0
        nearby_parking = 0
        
        search_radius = self.road_width_cells * 2
        
        for dx in range(-search_radius, search_radius + 1):
            for dy in range(-search_radius, search_radius + 1):
                check_x, check_y = parking_x + dx, parking_y + dy
                
                if not self.field.is_valid_position(check_x, check_y):
                    continue
                
                distance = np.sqrt(dx**2 + dy**2)
                if distance <= search_radius:
                    cell_type = self.field.grid[check_x, check_y]
                    if cell_type == CellType.ROAD:
                        nearby_roads += 1
                    elif cell_type == CellType.PARKING:
                        nearby_parking += 1
        
        # High penalty if too many parking spaces near limited roads
        if nearby_roads > 0:
            density_ratio = nearby_parking / nearby_roads
            if density_ratio > 3.0:  # More than 3 parking spaces per road cell
                penalty += 50.0 * (density_ratio - 3.0)
        
        return penalty
    
    def _entrance_access_penalty(self, parking_x: int, parking_y: int) -> float:
        """Calculate penalty for interfering with entrance access."""
        penalty = 0.0
        
        for entrance_x, entrance_y in self.field.entrances:
            distance = np.sqrt((parking_x - entrance_x)**2 + (parking_y - entrance_y)**2)
            
            # High penalty for parking too close to entrances
            min_distance = self.road_width_cells * 2
            if distance < min_distance:
                penalty += 200.0 / (distance + 1)
        
        return penalty
    
    def _point_to_line_distance(self, px: int, py: int, 
                               x1: int, y1: int, x2: int, y2: int) -> float:
        """Calculate distance from point to line segment."""
        # Vector from line start to point
        dx_p = px - x1
        dy_p = py - y1
        
        # Vector along line
        dx_l = x2 - x1
        dy_l = y2 - y1
        
        # Length of line squared
        line_length_sq = dx_l**2 + dy_l**2
        
        if line_length_sq == 0:
            # Line is a point
            return np.sqrt(dx_p**2 + dy_p**2)
        
        # Project point onto line
        t = max(0, min(1, (dx_p * dx_l + dy_p * dy_l) / line_length_sq))
        
        # Closest point on line
        closest_x = x1 + t * dx_l
        closest_y = y1 + t * dy_l
        
        # Distance from point to closest point on line
        return np.sqrt((px - closest_x)**2 + (py - closest_y)**2)


class ConstraintManager:
    """
    Manages all constraints for the parking optimization problem.
    """
    
    def __init__(self, field: ParkingField, config: Dict):
        """
        Initialize constraint manager.
        
        Args:
            field: ParkingField instance
            config: Configuration dictionary with constraint parameters
        """
        self.field = field
        self.config = config
        
        self.validator = ConstraintValidator(
            field, 
            config['vehicle']['length'],
            config['vehicle']['width'],
            config['vehicle']['turning_radius']
        )
        
        self.traffic_constraints = TrafficFlowConstraints(
            field,
            config['roads']['width']
        )
    
    def evaluate_placement(self, parking_x: int, parking_y: int,
                          existing_spaces: List[Tuple[int, int]]) -> Dict[str, float]:
        """
        Evaluate a parking space placement against all constraints.
        
        Args:
            parking_x, parking_y: Grid coordinates of proposed parking space
            existing_spaces: List of existing parking space coordinates
            
        Returns:
            Dictionary with constraint scores and penalties
        """
        # Validate hard constraints
        constraint_results = self.validator.validate_all_constraints(
            parking_x, parking_y, existing_spaces
        )
        
        # Calculate soft constraint penalties
        traffic_penalty = self.traffic_constraints.calculate_traffic_flow_penalty(
            parking_x, parking_y
        )
        
        # Calculate walking distance
        walking_distance = self.field.get_walking_distance(parking_x, parking_y)
        
        return {
            'hard_constraints': constraint_results,
            'traffic_penalty': traffic_penalty,
            'walking_distance': walking_distance,
            'is_valid': all(constraint_results.values())
        }
