# Configuration for Quantum Annealing Parking Optimization

# Field Configuration
field:
  width: 100.0  # meters
  height: 60.0  # meters
  resolution: 0.5  # grid resolution in meters
  
# Entrance Configuration
entrances:
  - location: [0, 30]    # Left entrance (x, y)
    direction: "east"    # Direction cars enter
  - location: [100, 30]  # Right entrance (x, y)
    direction: "west"    # Direction cars enter

# Vehicle Specifications
vehicle:
  length: 4.5      # meters
  width: 2.0       # meters
  turning_radius: 6.0  # minimum turning radius in meters
  
# Spacing Requirements
spacing:
  between_cars: 0.5    # minimum space between parked cars
  from_road: 1.0       # minimum distance from road edge
  maneuver_space: 2.5  # space needed for parking maneuvers

# Road Configuration
roads:
  width: 6.0           # road width for two-way traffic
  min_length: 10.0     # minimum road segment length
  intersection_radius: 8.0  # turning radius at intersections

# Optimization Parameters
optimization:
  algorithm: "simulated_annealing"  # or "quantum_annealing"
  max_iterations: 10000
  initial_temperature: 100.0
  cooling_rate: 0.95
  min_temperature: 0.01
  
# Quantum Annealing Specific (for D-Wave)
quantum:
  num_reads: 1000
  annealing_time: 20  # microseconds
  chain_strength: 1.0
  
# Objective Weights
weights:
  parking_capacity: 1.0      # Weight for maximizing parking spaces
  walking_distance: 0.3      # Weight for minimizing walking distance
  traffic_flow: 0.2          # Weight for traffic flow efficiency
  space_utilization: 0.1     # Weight for efficient space usage

# Constraints
constraints:
  max_walking_distance: 100.0  # maximum acceptable walking distance
  min_parking_spaces: 50       # minimum required parking spaces
  accessibility_required: true  # ensure all spaces are accessible
  
# Visualization
visualization:
  show_grid: true
  show_roads: true
  show_entrances: true
  color_scheme: "default"
  save_format: "png"
  dpi: 300
