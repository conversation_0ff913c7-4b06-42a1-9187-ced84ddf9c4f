"""
Test Suite for Parking Optimization System

This module contains comprehensive tests for all components of the
quantum annealing parking optimization system.
"""

import unittest
import numpy as np
import tempfile
import os
import sys

# Add src directory to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from models.field_representation import ParkingField, CellType
from models.qubo_formulation import ParkingQUBO
from algorithms.quantum_annealing import <PERSON><PERSON>latedA<PERSON><PERSON><PERSON>, AnnealingOptimizer
from constraints.physical_constraints import Constraint<PERSON><PERSON>da<PERSON>, ConstraintManager
from utils.performance_metrics import MetricsCalculator, PerformanceMetrics
from visualization.plot_layout import ParkingLayoutVisualizer


class TestParkingField(unittest.TestCase):
    """Test cases for ParkingField class."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.field = ParkingField(width=50.0, height=30.0, resolution=1.0)
    
    def test_field_initialization(self):
        """Test field initialization."""
        self.assertEqual(self.field.width, 50.0)
        self.assertEqual(self.field.height, 30.0)
        self.assertEqual(self.field.resolution, 1.0)
        self.assertEqual(self.field.grid_width, 50)
        self.assertEqual(self.field.grid_height, 30)
    
    def test_coordinate_conversion(self):
        """Test coordinate conversion between world and grid."""
        # Test world to grid conversion
        grid_x, grid_y = self.field.world_to_grid(25.0, 15.0)
        self.assertEqual(grid_x, 25)
        self.assertEqual(grid_y, 15)
        
        # Test grid to world conversion
        world_x, world_y = self.field.grid_to_world(25, 15)
        self.assertEqual(world_x, 25.5)  # Center of cell
        self.assertEqual(world_y, 15.5)
    
    def test_entrance_placement(self):
        """Test entrance placement."""
        self.field.add_entrance(0.0, 15.0, width=6.0)
        
        # Check that entrance cells are marked
        entrance_count = 0
        for i in range(self.field.grid_width):
            for j in range(self.field.grid_height):
                if self.field.grid[i, j] == CellType.ENTRANCE:
                    entrance_count += 1
        
        self.assertGreater(entrance_count, 0)
        self.assertGreater(len(self.field.entrances), 0)
    
    def test_parking_space_placement(self):
        """Test parking space placement."""
        # Place a parking space
        success = self.field.place_parking_space(25.0, 15.0, length=4.5, width=2.0)
        self.assertTrue(success)
        
        # Check that parking cells are marked
        parking_count = 0
        for i in range(self.field.grid_width):
            for j in range(self.field.grid_height):
                if self.field.grid[i, j] == CellType.PARKING:
                    parking_count += 1
        
        self.assertGreater(parking_count, 0)
        self.assertGreater(len(self.field.parking_spaces), 0)
    
    def test_road_placement(self):
        """Test road placement."""
        self.field.add_road_segment(0.0, 15.0, 50.0, 15.0, width=6.0)
        
        # Check that road cells are marked
        road_count = 0
        for i in range(self.field.grid_width):
            for j in range(self.field.grid_height):
                if self.field.grid[i, j] == CellType.ROAD:
                    road_count += 1
        
        self.assertGreater(road_count, 0)
        self.assertGreater(len(self.field.roads), 0)


class TestQUBOFormulation(unittest.TestCase):
    """Test cases for QUBO formulation."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.qubo = ParkingQUBO(
            field_width=50.0,
            field_height=30.0,
            resolution=2.0,
            car_length=4.5,
            car_width=2.0,
            road_width=6.0,
            entrances=[(0, 15), (50, 15)],
            weights={'parking_capacity': 1.0, 'walking_distance': 0.3, 'traffic_flow': 0.2}
        )
    
    def test_variable_initialization(self):
        """Test variable initialization."""
        self.assertGreater(len(self.qubo.parking_vars), 0)
        self.assertGreater(len(self.qubo.road_vars), 0)
        self.assertGreater(self.qubo.var_count, 0)
    
    def test_qubo_matrix_construction(self):
        """Test QUBO matrix construction."""
        Q = self.qubo.build_qubo_matrix()
        
        # Check matrix properties
        self.assertEqual(Q.shape[0], Q.shape[1])  # Square matrix
        self.assertEqual(Q.shape[0], self.qubo.var_count)
        self.assertTrue(np.allclose(Q, Q.T))  # Symmetric matrix
    
    def test_variable_mapping(self):
        """Test variable mapping."""
        mapping = self.qubo.get_variable_mapping()
        
        self.assertEqual(len(mapping), self.qubo.var_count)
        
        # Check that all variable types are valid
        for var_idx, (var_type, i, j) in mapping.items():
            self.assertIn(var_type, ['parking', 'road'])
            self.assertIsInstance(i, int)
            self.assertIsInstance(j, int)


class TestSimulatedAnnealing(unittest.TestCase):
    """Test cases for simulated annealing algorithm."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.annealer = SimulatedAnnealer(
            initial_temperature=10.0,
            cooling_rate=0.9,
            min_temperature=0.1,
            max_iterations=100
        )
    
    def test_simple_optimization(self):
        """Test optimization on a simple QUBO problem."""
        # Create a simple 4x4 QUBO matrix
        Q = np.array([
            [-1, 0.5, 0, 0],
            [0.5, -1, 0.5, 0],
            [0, 0.5, -1, 0.5],
            [0, 0, 0.5, -1]
        ])
        
        result = self.annealer.solve(Q)
        
        # Check result structure
        self.assertIn('solution', result)
        self.assertIn('energy', result)
        self.assertIn('iterations', result)
        self.assertIn('solve_time', result)
        
        # Check solution properties
        solution = result['solution']
        self.assertEqual(len(solution), 4)
        self.assertTrue(all(x in [0, 1] for x in solution))
    
    def test_energy_calculation(self):
        """Test energy calculation."""
        Q = np.array([[1, 0.5], [0.5, 1]])
        x = np.array([1, 0])
        
        energy = self.annealer._calculate_energy(Q, x)
        expected_energy = 1  # x^T Q x = [1,0] * [[1,0.5],[0.5,1]] * [1,0]^T = 1
        
        self.assertEqual(energy, expected_energy)


class TestConstraints(unittest.TestCase):
    """Test cases for constraint validation."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.field = ParkingField(width=50.0, height=30.0, resolution=1.0)
        self.field.add_entrance(0.0, 15.0)
        self.field.add_road_segment(0.0, 15.0, 50.0, 15.0)
        
        self.validator = ConstraintValidator(
            self.field,
            car_length=4.5,
            car_width=2.0,
            turning_radius=6.0
        )
    
    def test_spacing_constraint(self):
        """Test spacing constraint validation."""
        # Test valid spacing
        valid = self.validator.validate_spacing_constraint(10, 10, 20, 20, min_spacing=0.5)
        self.assertTrue(valid)
        
        # Test invalid spacing (too close)
        invalid = self.validator.validate_spacing_constraint(10, 10, 11, 11, min_spacing=0.5)
        self.assertFalse(invalid)
    
    def test_boundary_constraint(self):
        """Test boundary constraint validation."""
        # Test valid position (center of field)
        valid = self.validator.validate_boundary_constraint(25, 15)
        self.assertTrue(valid)
        
        # Test invalid position (too close to edge)
        invalid = self.validator.validate_boundary_constraint(1, 1)
        self.assertFalse(invalid)
    
    def test_accessibility_constraint(self):
        """Test accessibility constraint validation."""
        # Position near road should be accessible
        accessible = self.validator.validate_accessibility_constraint(25, 18, max_access_distance=5.0)
        self.assertTrue(accessible)

        # Position far from road should not be accessible
        not_accessible = self.validator.validate_accessibility_constraint(25, 5, max_access_distance=5.0)
        self.assertFalse(not_accessible)


class TestPerformanceMetrics(unittest.TestCase):
    """Test cases for performance metrics calculation."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.field = ParkingField(width=50.0, height=30.0, resolution=1.0)
        self.field.add_entrance(0.0, 15.0)
        self.field.add_entrance(50.0, 15.0)
        
        config = {
            'vehicle': {'length': 4.5, 'width': 2.0, 'turning_radius': 6.0},
            'spacing': {'between_cars': 0.5},
            'weights': {'parking_capacity': 1.0, 'walking_distance': 0.3, 'traffic_flow': 0.2}
        }
        
        self.calculator = MetricsCalculator(self.field, config)
    
    def test_metrics_calculation(self):
        """Test metrics calculation for a sample solution."""
        # Create a dummy solution and variable mapping
        solution = np.array([1, 0, 1, 0, 1])
        variable_mapping = {
            0: ('parking', 10, 10),
            1: ('parking', 15, 15),
            2: ('parking', 20, 20),
            3: ('parking', 25, 25),
            4: ('parking', 30, 30)
        }
        
        optimization_result = {
            'energy': -10.0,
            'solve_time': 5.0,
            'iterations': 1000
        }
        
        metrics = self.calculator.calculate_metrics(
            solution, variable_mapping, optimization_result
        )
        
        # Check that metrics object is created correctly
        self.assertIsInstance(metrics, PerformanceMetrics)
        self.assertEqual(metrics.total_parking_spaces, 3)  # Three 1s in solution
        self.assertGreater(metrics.overall_score, 0)
        self.assertEqual(metrics.solve_time, 5.0)


class TestIntegration(unittest.TestCase):
    """Integration tests for the complete system."""
    
    def test_end_to_end_optimization(self):
        """Test complete optimization pipeline."""
        # Create a small test problem
        field = ParkingField(width=20.0, height=20.0, resolution=2.0)
        field.add_entrance(0.0, 10.0)
        field.add_entrance(20.0, 10.0)
        field.add_road_segment(0.0, 10.0, 20.0, 10.0)
        
        # Create QUBO formulation
        qubo = ParkingQUBO(
            field_width=20.0,
            field_height=20.0,
            resolution=2.0,
            car_length=4.5,
            car_width=2.0,
            road_width=6.0,
            entrances=[(0, 10), (20, 10)],
            weights={'parking_capacity': 1.0, 'walking_distance': 0.3, 'traffic_flow': 0.2}
        )
        
        # Build QUBO matrix
        Q = qubo.build_qubo_matrix()
        
        # Run optimization
        optimizer = AnnealingOptimizer(
            algorithm='simulated_annealing',
            max_iterations=100,
            initial_temperature=10.0
        )
        
        result = optimizer.optimize(Q)
        
        # Check that we get a valid result
        self.assertIn('solution', result)
        self.assertIn('energy', result)
        self.assertEqual(len(result['solution']), Q.shape[0])
        
        # Calculate metrics
        config = {
            'vehicle': {'length': 4.5, 'width': 2.0, 'turning_radius': 6.0},
            'spacing': {'between_cars': 0.5},
            'weights': {'parking_capacity': 1.0, 'walking_distance': 0.3, 'traffic_flow': 0.2}
        }
        
        calculator = MetricsCalculator(field, config)
        variable_mapping = qubo.get_variable_mapping()
        
        metrics = calculator.calculate_metrics(
            result['solution'], variable_mapping, result
        )
        
        # Check that metrics are reasonable
        self.assertIsInstance(metrics, PerformanceMetrics)
        self.assertGreaterEqual(metrics.overall_score, 0)
        self.assertLessEqual(metrics.overall_score, 100)


def run_tests():
    """Run all tests."""
    # Create test suite
    test_suite = unittest.TestSuite()
    
    # Add test cases
    test_classes = [
        TestParkingField,
        TestQUBOFormulation,
        TestSimulatedAnnealing,
        TestConstraints,
        TestPerformanceMetrics,
        TestIntegration
    ]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    return result.wasSuccessful()


if __name__ == '__main__':
    success = run_tests()
    if success:
        print("\nAll tests passed! ✅")
    else:
        print("\nSome tests failed! ❌")
        exit(1)
