# Quantum Annealing for Car Parking Space Optimization

## Project Overview
This project implements a quantum annealing solution to optimize car parking space allocation in an empty field. The solution maximizes parking capacity while minimizing walking distances and ensuring efficient traffic flow.

## Problem Setup
- **Field Model**: 2D grid representation of an empty field
- **Entrances**: Two entrance points for vehicle access
- **Roads**: Green pathways connecting entrances to parking areas
- **Vehicles**: Standard car dimensions with turning radius requirements

## Optimization Objectives
1. **Maximize** the number of parking spaces
2. **Minimize** total walking distance from parking to entrances
3. **Ensure** efficient traffic flow between entrances and parking areas
4. **Maintain** adequate spacing for vehicle maneuvering

## Technical Approach
- **Algorithm**: Quantum annealing (simulated annealing + D-Wave compatibility)
- **Formulation**: Quadratic Unconstrained Binary Optimization (QUBO)
- **Constraints**: Distance, accessibility, traffic flow, field boundaries

## Default Parameters
- **Field Size**: 100m x 60m (configurable)
- **Car Dimensions**: 4.5m x 2.0m (length x width)
- **Minimum Spacing**: 0.5m between cars
- **Turning Radius**: 6m minimum
- **Road Width**: 6m for two-way traffic
- **Entrance Locations**: (0, 30) and (100, 30) - center of left and right edges

## Project Structure
```
├── src/
│   ├── models/          # Problem formulation and QUBO models
│   ├── algorithms/      # Quantum annealing implementations
│   ├── constraints/     # Physical and logical constraints
│   ├── visualization/   # Plotting and display tools
│   └── utils/          # Helper functions and utilities
├── tests/              # Unit tests and validation
├── examples/           # Sample configurations and demos
├── data/              # Input/output data files
└── docs/              # Documentation and analysis
```

## Installation and Usage

### Quick Start
```bash
# Install dependencies
pip install -r requirements.txt

# Run tests to verify installation
python -m pytest tests/test_optimization.py -v

# Run optimization with default settings
python src/main.py

# Run with custom field dimensions
python src/main.py --field-width 80 --field-height 50

# Compare different algorithms
python src/main.py --compare

# Run demonstration with multiple field configurations
python examples/demo_optimization.py
```

### Command Line Options
```bash
python src/main.py --help
```

Available options:
- `--config`: Path to configuration file (default: config.yaml)
- `--output`: Output directory for results (default: results)
- `--algorithm`: Choose optimization algorithm (simulated_annealing, quantum_annealing, hybrid)
- `--compare`: Compare multiple algorithms
- `--field-width`: Override field width in meters
- `--field-height`: Override field height in meters

## Dependencies
- numpy: Numerical computations
- matplotlib: Visualization
- scipy: Optimization algorithms
- networkx: Graph algorithms for road networks
- dimod: D-Wave quantum annealing interface
- dwave-ocean-sdk: Quantum computing tools (optional)
