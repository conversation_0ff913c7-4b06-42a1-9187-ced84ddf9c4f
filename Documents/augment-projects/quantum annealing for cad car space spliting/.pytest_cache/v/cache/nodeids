["tests/test_optimization.py::TestConstraints::test_accessibility_constraint", "tests/test_optimization.py::TestConstraints::test_boundary_constraint", "tests/test_optimization.py::TestConstraints::test_spacing_constraint", "tests/test_optimization.py::TestIntegration::test_end_to_end_optimization", "tests/test_optimization.py::TestParkingField::test_coordinate_conversion", "tests/test_optimization.py::TestParkingField::test_entrance_placement", "tests/test_optimization.py::TestParkingField::test_field_initialization", "tests/test_optimization.py::TestParkingField::test_parking_space_placement", "tests/test_optimization.py::TestParkingField::test_road_placement", "tests/test_optimization.py::TestPerformanceMetrics::test_metrics_calculation", "tests/test_optimization.py::TestQUBOFormulation::test_qubo_matrix_construction", "tests/test_optimization.py::TestQUBOFormulation::test_variable_initialization", "tests/test_optimization.py::TestQUBOFormulation::test_variable_mapping", "tests/test_optimization.py::TestSimulatedAnnealing::test_energy_calculation", "tests/test_optimization.py::TestSimulatedAnnealing::test_simple_optimization"]