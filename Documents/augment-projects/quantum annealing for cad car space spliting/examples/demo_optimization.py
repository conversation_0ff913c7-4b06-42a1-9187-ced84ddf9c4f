"""
Demonstration Script for Quantum Annealing Parking Optimization

This script demonstrates the complete parking optimization system with
different field configurations and algorithms.
"""

import sys
import os
import numpy as np
import matplotlib.pyplot as plt

# Add src directory to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from models.field_representation import ParkingField, CellType
from models.qubo_formulation import ParkingQUBO
from algorithms.quantum_annealing import AnnealingOptimizer
from visualization.plot_layout import ParkingLayoutVisualizer
from utils.performance_metrics import MetricsCalculator, BenchmarkComparator


def create_sample_field_1():
    """Create a rectangular field with entrances on opposite sides."""
    field = ParkingField(width=100.0, height=60.0, resolution=1.0)
    
    # Add entrances
    field.add_entrance(0.0, 30.0, width=8.0)    # Left entrance
    field.add_entrance(100.0, 30.0, width=8.0)  # Right entrance
    
    # Add main road connecting entrances
    field.add_road_segment(0.0, 30.0, 100.0, 30.0, width=8.0)
    
    # Add perpendicular roads for access
    field.add_road_segment(25.0, 10.0, 25.0, 50.0, width=6.0)
    field.add_road_segment(50.0, 10.0, 50.0, 50.0, width=6.0)
    field.add_road_segment(75.0, 10.0, 75.0, 50.0, width=6.0)
    
    return field


def create_sample_field_2():
    """Create an L-shaped field with corner entrances."""
    field = ParkingField(width=80.0, height=80.0, resolution=1.0)
    
    # Block out part of the field to create L-shape
    for i in range(40, 80):
        for j in range(40, 80):
            if field.is_valid_position(i, j):
                field.grid[i, j] = CellType.BLOCKED
    
    # Add entrances
    field.add_entrance(0.0, 20.0, width=6.0)    # Left entrance
    field.add_entrance(20.0, 0.0, width=6.0)    # Bottom entrance
    
    # Add road network
    field.add_road_segment(0.0, 20.0, 35.0, 20.0, width=6.0)  # Horizontal road
    field.add_road_segment(20.0, 0.0, 20.0, 35.0, width=6.0)  # Vertical road
    field.add_road_segment(20.0, 20.0, 35.0, 35.0, width=6.0) # Connecting road
    
    return field


def create_sample_field_3():
    """Create a circular field with multiple entrances."""
    field = ParkingField(width=80.0, height=80.0, resolution=1.0)
    
    # Create circular boundary
    center_x, center_y = 40, 40
    radius = 35
    
    for i in range(field.grid_width):
        for j in range(field.grid_height):
            distance = np.sqrt((i - center_x)**2 + (j - center_y)**2)
            if distance > radius:
                field.grid[i, j] = CellType.BLOCKED
    
    # Add multiple entrances around the perimeter
    field.add_entrance(40.0, 5.0, width=6.0)   # North
    field.add_entrance(75.0, 40.0, width=6.0)  # East
    field.add_entrance(40.0, 75.0, width=6.0)  # South
    field.add_entrance(5.0, 40.0, width=6.0)   # West
    
    # Add radial roads
    field.add_road_segment(40.0, 5.0, 40.0, 75.0, width=6.0)   # North-South
    field.add_road_segment(5.0, 40.0, 75.0, 40.0, width=6.0)   # East-West
    
    # Add circular road
    angles = np.linspace(0, 2*np.pi, 32)
    road_radius = 20
    for i in range(len(angles)-1):
        x1 = center_x + road_radius * np.cos(angles[i])
        y1 = center_y + road_radius * np.sin(angles[i])
        x2 = center_x + road_radius * np.cos(angles[i+1])
        y2 = center_y + road_radius * np.sin(angles[i+1])
        field.add_road_segment(x1, y1, x2, y2, width=4.0)
    
    return field


def optimize_field(field, field_name, algorithm='simulated_annealing'):
    """Optimize parking layout for a given field."""
    print(f"\n{'='*60}")
    print(f"Optimizing {field_name} with {algorithm}")
    print(f"{'='*60}")
    
    # Get entrances for QUBO formulation
    entrances = []
    for entrance_x, entrance_y in field.entrances:
        world_x, world_y = field.grid_to_world(entrance_x, entrance_y)
        entrances.append((world_x, world_y))
    
    # Create QUBO formulation
    qubo = ParkingQUBO(
        field_width=field.width,
        field_height=field.height,
        resolution=field.resolution,
        car_length=4.5,
        car_width=2.0,
        road_width=6.0,
        entrances=entrances,
        weights={
            'parking_capacity': 1.0,
            'walking_distance': 0.3,
            'traffic_flow': 0.2,
            'space_utilization': 0.1
        }
    )
    
    # Build QUBO matrix
    print("Building QUBO formulation...")
    Q = qubo.build_qubo_matrix()
    print(f"QUBO matrix size: {Q.shape[0]} x {Q.shape[1]}")
    
    # Create optimizer
    if algorithm == 'simulated_annealing':
        optimizer = AnnealingOptimizer(
            algorithm='simulated_annealing',
            max_iterations=5000,
            initial_temperature=100.0,
            cooling_rate=0.95,
            min_temperature=0.01
        )
    else:
        optimizer = AnnealingOptimizer(algorithm=algorithm)
    
    # Run optimization
    print("Running optimization...")
    result = optimizer.optimize(Q)
    
    # Apply solution to field
    variable_mapping = qubo.get_variable_mapping()
    apply_solution_to_field(field, result['solution'], variable_mapping)
    
    # Calculate metrics
    config = {
        'vehicle': {'length': 4.5, 'width': 2.0, 'turning_radius': 6.0},
        'spacing': {'between_cars': 0.5},
        'weights': {'parking_capacity': 1.0, 'walking_distance': 0.3, 'traffic_flow': 0.2}
    }
    
    calculator = MetricsCalculator(field, config)
    metrics = calculator.calculate_metrics(result['solution'], variable_mapping, result)
    
    # Print results
    print(f"\nResults for {field_name}:")
    print(f"  Parking Spaces: {metrics.total_parking_spaces}")
    print(f"  Space Utilization: {metrics.space_utilization:.1f}%")
    print(f"  Average Walking Distance: {metrics.avg_walking_distance:.1f}m")
    print(f"  Overall Score: {metrics.overall_score:.1f}/100")
    print(f"  Solve Time: {metrics.solve_time:.2f}s")
    
    return field, result, metrics


def apply_solution_to_field(field, solution, variable_mapping):
    """Apply optimization solution to field representation."""
    # Clear existing parking spaces
    for i in range(field.grid_width):
        for j in range(field.grid_height):
            if field.grid[i, j] == CellType.PARKING:
                field.grid[i, j] = CellType.EMPTY
    
    field.parking_spaces.clear()
    
    # Apply solution
    for var_idx, (var_type, i, j) in variable_mapping.items():
        if var_idx < len(solution) and solution[var_idx] == 1 and var_type == 'parking':
            x, y = field.grid_to_world(i, j)
            field.place_parking_space(x, y)


def visualize_results(fields_and_results, save_plots=True):
    """Create visualizations for all optimization results."""
    print(f"\n{'='*60}")
    print("Generating Visualizations")
    print(f"{'='*60}")
    
    # Create comparison plot
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    axes = axes.flatten()
    
    for idx, (field_name, field, result, metrics) in enumerate(fields_and_results):
        if idx >= 4:  # Only plot first 4 results
            break
            
        visualizer = ParkingLayoutVisualizer(field, figsize=(8, 6))
        
        # Create individual plot
        individual_fig = visualizer.plot_field_layout(
            title=f"{field_name} - Optimized Layout",
            show_grid=False
        )
        
        if save_plots:
            individual_fig.savefig(f'results_{field_name.lower().replace(" ", "_")}.png', 
                                 dpi=300, bbox_inches='tight')
        
        # Add to comparison plot
        ax = axes[idx]
        
        # Create color map for the grid
        color_map = np.zeros((field.grid_width, field.grid_height, 3))
        colors = {
            'empty': (0.94, 0.94, 0.94),
            'parking': (0.30, 0.69, 0.31),
            'road': (0.46, 0.46, 0.46),
            'entrance': (1.0, 0.34, 0.13),
            'blocked': (0.96, 0.26, 0.21)
        }
        
        for i in range(field.grid_width):
            for j in range(field.grid_height):
                cell_type = field.grid[i, j]
                
                if cell_type == CellType.EMPTY:
                    color = colors['empty']
                elif cell_type == CellType.PARKING:
                    color = colors['parking']
                elif cell_type == CellType.ROAD:
                    color = colors['road']
                elif cell_type == CellType.ENTRANCE:
                    color = colors['entrance']
                else:  # BLOCKED
                    color = colors['blocked']
                
                color_map[i, j] = color
        
        ax.imshow(color_map.transpose(1, 0, 2), origin='lower', 
                 extent=[0, field.width, 0, field.height])
        ax.set_title(f"{field_name}\n{metrics.total_parking_spaces} spaces, "
                    f"Score: {metrics.overall_score:.1f}")
        ax.set_xlabel('X (meters)')
        ax.set_ylabel('Y (meters)')
        ax.set_aspect('equal')
    
    # Hide unused subplots
    for idx in range(len(fields_and_results), 4):
        axes[idx].set_visible(False)
    
    plt.tight_layout()
    
    if save_plots:
        plt.savefig('comparison_all_fields.png', dpi=300, bbox_inches='tight')
    
    plt.show()


def compare_algorithms_demo():
    """Demonstrate algorithm comparison on a single field."""
    print(f"\n{'='*60}")
    print("Algorithm Comparison Demo")
    print(f"{'='*60}")
    
    # Use the rectangular field for comparison
    field = create_sample_field_1()
    
    algorithms = ['simulated_annealing']
    
    # Check if D-Wave is available (would need actual D-Wave access)
    # algorithms.append('quantum_annealing')
    
    comparator = BenchmarkComparator()
    
    for algorithm in algorithms:
        print(f"\nTesting {algorithm}...")
        optimized_field, result, metrics = optimize_field(
            create_sample_field_1(),  # Create fresh field for each test
            f"Rectangular Field ({algorithm})",
            algorithm
        )
        
        comparator.add_result(algorithm, metrics, {'algorithm': algorithm})
    
    # Generate comparison report
    report = comparator.generate_comparison_report()
    
    print(f"\n{'='*60}")
    print("Algorithm Comparison Results")
    print(f"{'='*60}")
    
    for recommendation in report.get('recommendations', []):
        print(f"• {recommendation}")


def main():
    """Main demonstration function."""
    print("Quantum Annealing Parking Optimization - Demonstration")
    print("="*60)
    
    # Create sample fields
    print("Creating sample field configurations...")
    
    fields = [
        ("Rectangular Field", create_sample_field_1()),
        ("L-Shaped Field", create_sample_field_2()),
        ("Circular Field", create_sample_field_3())
    ]
    
    # Optimize each field
    results = []
    
    for field_name, field in fields:
        optimized_field, result, metrics = optimize_field(field, field_name)
        results.append((field_name, optimized_field, result, metrics))
    
    # Create visualizations
    visualize_results(results, save_plots=True)
    
    # Compare algorithms (if multiple are available)
    compare_algorithms_demo()
    
    print(f"\n{'='*60}")
    print("Demonstration Complete!")
    print("Check the generated PNG files for visualizations.")
    print(f"{'='*60}")


if __name__ == "__main__":
    main()
