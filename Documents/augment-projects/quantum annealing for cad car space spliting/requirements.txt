# Core scientific computing
numpy>=1.21.0
scipy>=1.7.0
matplotlib>=3.5.0

# Graph algorithms and network analysis
networkx>=2.6.0

# Quantum computing and optimization
dimod>=0.12.0
dwave-ocean-sdk>=6.0.0
dwave-system>=1.15.0

# Additional optimization tools
cvxpy>=1.2.0
pulp>=2.6.0

# Data handling and utilities
pandas>=1.3.0
pyyaml>=6.0
tqdm>=4.62.0

# Visualization enhancements
seaborn>=0.11.0
plotly>=5.0.0

# Development and testing
pytest>=6.2.0
pytest-cov>=3.0.0
black>=22.0.0
flake8>=4.0.0

# Optional: For advanced quantum features
# qiskit>=0.39.0
# cirq>=1.0.0
